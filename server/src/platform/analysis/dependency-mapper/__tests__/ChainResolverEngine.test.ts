/**
 * ============================================================================
 * OA FRAMEWORK - M0.1 ENTERPRISE ENHANCEMENT IMPLEMENTATION
 * Chain Resolver Engine Test Suite
 * ============================================================================
 * 
 * @fileoverview Comprehensive test suite for ChainResolverEngine
 * @version 2.3.0
 * @since 2025-09-17
 * <AUTHOR> & CEO, <PERSON><PERSON><PERSON>. Consultancy
 * 
 * 🎯 M0.1 ENTERPRISE ENHANCEMENT TASK: ENH-TSK-01.SUB-01.1.IMP-04
 * 📋 COMPONENT: Chain Resolver Engine - Chain Resolution Logic
 * 🔧 REFACTOR: REF-03 - Chain resolution logic (400 LOC)
 * 
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level enterprise-grade
 * @base-class BaseTrackingService
 * @memory-boundaries enforced
 * @resource-cleanup automatic
 * @timing-resilience circuit-breaker-enabled
 * @performance-target <6ms
 * @memory-footprint <20MB
 * @resilient-timing-integration dual-field-pattern
 * @memory-leak-prevention comprehensive
 * @resource-monitoring continuous
 * 
 * 📊 TESTING COVERAGE TARGETS
 * @line-coverage ≥95%
 * @branch-coverage ≥75%
 * @function-coverage 100%
 * @statement-coverage ≥95%
 * 
 * ============================================================================
 */

import { ChainResolverEngine } from '../ChainResolverEngine';
import { TDependencyMapperConfig, TChainResolutionResult, TDependencyChain } from '../../../../../../shared/src/types/platform/analysis/dependency-mapper-types';
import { TTrackingConfig } from '../../../../../../shared/src/types/platform/tracking/tracking-types';

describe('ChainResolverEngine', () => {
  let resolver: ChainResolverEngine;
  let mockResolverConfig: { maxResolutionDepth: number; resolutionTimeout: number; enableParallelResolution: boolean; maxConcurrentResolutions: number; conflictResolutionStrategies: string[]; optimizationEnabled: boolean; };
  let mockTrackingConfig: TTrackingConfig;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockResolverConfig = {
      maxResolutionDepth: 20,
      resolutionTimeout: 60000,
      enableParallelResolution: true,
      maxConcurrentResolutions: 2,
      conflictResolutionStrategies: ['automatic', 'manual', 'fallback'],
      optimizationEnabled: true
    };

    mockTrackingConfig = {
      service: {
        name: 'ChainResolverEngine',
        version: '1.0.0',
        environment: 'development',
        timeout: 30000,
        retry: {
          maxAttempts: 3,
          delay: 1000,
          backoffMultiplier: 2,
          maxDelay: 10000
        }
      },
      governance: {
        authority: 'President & CEO, E.Z. Consultancy',
        requiredCompliance: ['authority-validation', 'chain-resolution', 'enterprise-enhancement'],
        auditFrequency: 24,
        violationReporting: true
      },
      performance: {
        metricsEnabled: true,
        metricsInterval: 60000,
        monitoringEnabled: true,
        alertThresholds: {
          responseTime: 6000,
          errorRate: 5,
          memoryUsage: 1024,
          cpuUsage: 80
        }
      },
      logging: {
        level: 'info',
        format: 'json',
        rotation: true,
        maxFileSize: 10
      }
    };
  });

  afterEach(async () => {
    if (resolver) {
      await resolver.shutdown();
    }
  });

  describe('Constructor and Initialization', () => {
    it('should initialize with default configuration', () => {
      resolver = new ChainResolverEngine();
      expect(resolver).toBeInstanceOf(ChainResolverEngine);
    });

    it('should initialize with custom configuration', () => {
      resolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      expect(resolver).toBeInstanceOf(ChainResolverEngine);
    });

    it('should initialize resilient timing infrastructure', () => {
      resolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      expect((resolver as any)._resilientTimer).toBeDefined();
      expect((resolver as any)._metricsCollector).toBeDefined();
    });
  });

  describe('BaseTrackingService Integration', () => {
    beforeEach(() => {
      resolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
    });

    it('should implement required abstract methods', () => {
      expect((resolver as any).getServiceName()).toBe('ChainResolverEngine');
      expect((resolver as any).getServiceVersion()).toBe('1.0.0');
    });

    it('should handle initialization lifecycle', async () => {
      await resolver.initialize();
      expect((resolver as any)._isInitialized).toBe(true);
    });

    it('should handle shutdown lifecycle', async () => {
      await resolver.initialize();
      await resolver.shutdown();
      expect((resolver as any)._isInitialized).toBe(false);
    });

    it('should track operations', async () => {
      await resolver.initialize();
      // Test that track method exists and can be called
      expect(typeof resolver.track).toBe('function');
    });

    it('should validate operations', async () => {
      await resolver.initialize();
      const validationResult = await resolver.validate();
      expect(validationResult).toBeDefined();
    });
  });

  describe('IChainResolver Interface Implementation', () => {
    beforeEach(async () => {
      resolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      await resolver.initialize();
    });

    it('should resolve dependency chains for a graph', async () => {
      const graphId = 'test-graph';
      const result = await resolver.resolveDependencyChains(graphId);
      
      expect(result).toBeDefined();
      expect(result.resolvedChains).toBeDefined();
      expect(result.unresolvedDependencies).toBeDefined();
      expect(result.conflicts).toBeDefined();
      expect(result.metadata).toBeDefined();
    });

    it('should resolve conflicts in dependency chains', async () => {
      const conflicts: TChainResolutionResult['conflicts'] = [
        {
          conflictId: 'test-conflict',
          type: 'circular' as const,
          description: 'Test circular dependency',
          affectedComponents: ['comp1', 'comp2'],
          resolutionStrategy: 'break-cycle'
        }
      ];
      
      const result = await resolver.resolveConflicts(conflicts);
      
      expect(result).toBeDefined();
      expect(result.resolvedChains).toBeDefined();
      expect(result.conflicts).toBeDefined();
    });

    it('should optimize chain resolution for a specific chain', async () => {
      const chainId = 'test-chain';
      const result = await resolver.optimizeChainResolution(chainId);

      expect(result).toBeDefined();
      expect(result.chainId).toBeDefined();
      expect(result.chainId).toMatch(/^chain-\d+-[a-z0-9]+$/);
      expect(result.nodes).toBeDefined();
      expect(result.metrics).toBeDefined();
    });
  });

  describe('Chain Resolution Strategies', () => {
    beforeEach(async () => {
      resolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      await resolver.initialize();
    });

    it('should apply resolution strategies', async () => {
      const conflicts: TChainResolutionResult['conflicts'] = [
        {
          conflictId: 'strategy-test',
          type: 'version' as const,
          description: 'Version conflict',
          affectedComponents: ['comp1'],
          resolutionStrategy: 'version-pinning'
        }
      ];
      
      // Test that conflicts can be processed
      const result = await resolver.resolveConflicts(conflicts);
      expect(result).toBeDefined();
    });

    it('should optimize chains', async () => {
      const chainId = 'optimization-test-chain';

      // Access private method for testing
      const optimizeChain = (resolver as any)._optimizeChain;
      const result = await optimizeChain.call(resolver, chainId);

      expect(result).toBeDefined();
      expect(result.chainId).toBeDefined();
      expect(result.chainId).toMatch(/^chain-\d+-[a-z0-9]+$/);
    });

    it('should generate resolution IDs', () => {
      // Access private method for testing
      const generateResolutionId = (resolver as any)._generateResolutionId;
      const id = generateResolutionId.call(resolver);
      
      expect(typeof id).toBe('string');
      expect(id).toMatch(/^resolution-\d+-[a-z0-9]+$/);
    });

    it('should generate chain IDs', () => {
      // Access private method for testing
      const generateChainId = (resolver as any)._generateChainId;
      const id = generateChainId.call(resolver);
      
      expect(typeof id).toBe('string');
      expect(id).toMatch(/^chain-\d+-[a-z0-9]+$/);
    });
  });

  describe('Performance and Memory Safety', () => {
    beforeEach(async () => {
      resolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      await resolver.initialize();
    });

    it('should complete operations within performance targets', async () => {
      const startTime = Date.now();
      await resolver.resolveDependencyChains('test-graph');
      const duration = Date.now() - startTime;
      
      expect(duration).toBeLessThan(6); // <6ms target for resolver
    });

    it('should enforce memory boundaries', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform multiple resolutions
      for (let i = 0; i < 10; i++) {
        await resolver.resolveDependencyChains(`graph-${i}`);
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024; // MB
      
      expect(memoryIncrease).toBeLessThan(20); // <20MB target for resolver
    });

    it('should handle concurrent resolutions', async () => {
      const operations = Array.from({ length: 3 }, (_, i) => 
        resolver.resolveDependencyChains(`concurrent-graph-${i}`)
      );
      
      await expect(Promise.all(operations)).resolves.toBeDefined();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    beforeEach(async () => {
      resolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      await resolver.initialize();
    });

    it('should handle invalid graph IDs', async () => {
      await expect(resolver.resolveDependencyChains('')).rejects.toThrow();
      await expect(resolver.resolveDependencyChains(null as any)).rejects.toThrow();
    });

    it('should handle empty conflicts array', async () => {
      const result = await resolver.resolveConflicts([]);
      expect(result).toBeDefined();
      expect(result.conflicts).toHaveLength(0);
    });

    it('should handle invalid chain IDs for optimization', async () => {
      await expect(resolver.optimizeChainResolution('')).rejects.toThrow();
      await expect(resolver.optimizeChainResolution(null as any)).rejects.toThrow();
    });

    it('should handle malformed conflicts', async () => {
      const malformedConflicts = [
        {
          conflictId: '',
          type: 'missing' as const,
          description: '',
          affectedComponents: [],
          resolutionStrategy: 'fallback'
        }
      ];

      // Should handle gracefully without throwing
      const result = await resolver.resolveConflicts(malformedConflicts);
      expect(result).toBeDefined();
    });

    it('should handle invalid conflicts parameter', async () => {
      // Test line 424 - Array.isArray check
      await expect(resolver.resolveConflicts(null as any)).rejects.toThrow();
      await expect(resolver.resolveConflicts('not-array' as any)).rejects.toThrow('Conflicts must be an array');
      await expect(resolver.resolveConflicts({} as any)).rejects.toThrow('Conflicts must be an array');
    });

    it('should handle errors during conflict resolution', async () => {
      // Mock _resolveConflict to throw error to test lines 439-444
      const originalMethod = (resolver as any)._resolveConflict;
      (resolver as any)._resolveConflict = jest.fn().mockImplementation(() => {
        throw new Error('Conflict resolution failed');
      });

      const conflicts = [
        {
          conflictId: 'error-test-conflict',
          type: 'circular' as const,
          description: 'Test error handling',
          affectedComponents: ['comp1'],
          resolutionStrategy: 'break-cycle'
        }
      ];

      try {
        const result = await resolver.resolveConflicts(conflicts);
        // Should handle error gracefully and put conflict in unresolved
        expect(result).toBeDefined();
        expect(result.conflicts).toContain(conflicts[0]);
      } finally {
        // Restore original method
        (resolver as any)._resolveConflict = originalMethod;
      }
    });

    it('should handle errors during chain resolution operations', async () => {
      // Mock _performChainResolution to throw error to test lines 484-488
      const originalMethod = (resolver as any)._performChainResolution;
      (resolver as any)._performChainResolution = jest.fn().mockImplementation(() => {
        throw new Error('Chain resolution failed');
      });

      try {
        await expect(resolver.resolveDependencyChains('error-graph')).rejects.toThrow('Chain resolution failed');
      } finally {
        // Restore original method
        (resolver as any)._performChainResolution = originalMethod;
      }
    });
  });

  describe('Conflict Resolution Types', () => {
    beforeEach(async () => {
      resolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      await resolver.initialize();
    });

    it('should handle circular dependency conflicts', async () => {
      const circularConflicts: TChainResolutionResult['conflicts'] = [
        {
          conflictId: 'circular-test',
          type: 'circular' as const,
          description: 'Circular dependency detected',
          affectedComponents: ['comp1', 'comp2', 'comp3'],
          resolutionStrategy: 'break-cycle'
        }
      ];
      
      const result = await resolver.resolveConflicts(circularConflicts);
      expect(result).toBeDefined();
      expect(result.conflicts.length).toBeLessThanOrEqual(circularConflicts.length);
    });

    it('should handle version conflicts', async () => {
      const versionConflicts: TChainResolutionResult['conflicts'] = [
        {
          conflictId: 'version-test',
          type: 'version' as const,
          description: 'Version mismatch',
          affectedComponents: ['lib1', 'lib2'],
          resolutionStrategy: 'version-pinning'
        }
      ];
      
      const result = await resolver.resolveConflicts(versionConflicts);
      expect(result).toBeDefined();
    });

    it('should handle dependency conflicts', async () => {
      const dependencyConflicts: TChainResolutionResult['conflicts'] = [
        {
          conflictId: 'dependency-test',
          type: 'constraint' as const,
          description: 'Dependency conflict',
          affectedComponents: ['service1', 'service2'],
          resolutionStrategy: 'alternative-dependency'
        }
      ];
      
      const result = await resolver.resolveConflicts(dependencyConflicts);
      expect(result).toBeDefined();
    });
  });

  describe('Chain Optimization', () => {
    beforeEach(async () => {
      resolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      await resolver.initialize();
    });

    it('should optimize chain performance', async () => {
      const chainId = 'performance-optimization-chain';
      const result = await resolver.optimizeChainResolution(chainId);

      expect(result).toBeDefined();
      expect(result.metadata).toBeDefined();
      expect(result.metadata.custom).toBeDefined();
    });

    it('should provide optimization metrics', async () => {
      const chainId = 'metrics-chain';
      const result = await resolver.optimizeChainResolution(chainId);

      expect(result.metadata.custom).toHaveProperty('chainId');
      expect(result.metadata.custom).toHaveProperty('status');
    });
  });

  describe('Cache Management and Memory Boundaries', () => {
    beforeEach(async () => {
      resolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      await resolver.initialize();
    });

    it('should return cached results when available', async () => {
      // Test lines 360-366 - cache hit scenario
      const graphId = 'cached-graph';

      // First call to populate cache
      const firstResult = await resolver.resolveDependencyChains(graphId);
      expect(firstResult).toBeDefined();

      // Second call should hit cache
      const secondResult = await resolver.resolveDependencyChains(graphId);
      expect(secondResult).toBeDefined();
      expect(secondResult.resolutionId).toBe(firstResult.resolutionId);
    });

    it('should handle concurrent resolution requests', async () => {
      // Test lines 370-372 - awaiting active resolution
      const graphId = 'concurrent-graph';

      // Start multiple concurrent requests
      const promise1 = resolver.resolveDependencyChains(graphId);
      const promise2 = resolver.resolveDependencyChains(graphId);
      const promise3 = resolver.resolveDependencyChains(graphId);

      const [result1, result2, result3] = await Promise.all([promise1, promise2, promise3]);

      // All should return the same result (from the single resolution)
      expect(result1.resolutionId).toBe(result2.resolutionId);
      expect(result2.resolutionId).toBe(result3.resolutionId);
    });

    it('should enforce memory boundaries on cache', async () => {
      // Test lines 684-689 - memory boundary enforcement
      const enforceMethod = (resolver as any)._enforceMemoryBoundaries;

      // Fill cache beyond limit
      const cache = (resolver as any)._resolutionCache;
      for (let i = 0; i < 250; i++) {
        cache.set(`graph-${i}`, {
          resolutionId: `res-${i}`,
          timestamp: new Date().toISOString(),
          status: 'success',
          resolvedChains: [],
          unresolvedDependencies: [],
          conflicts: [],
          metrics: { resolutionTime: 100, successRate: 1.0, conflictCount: 0, optimizationGains: 0 },
          metadata: { phase: 'test', progress: 100, priority: 'P1', tags: [], custom: {} }
        });
      }

      expect(cache.size).toBe(250);

      // Enforce boundaries
      enforceMethod.call(resolver);

      // Cache should be reduced to limit
      expect(cache.size).toBeLessThanOrEqual(200);
    });

    it('should collect resolution metrics', async () => {
      // Test lines 695-703 - metrics collection
      const collectMethod = (resolver as any)._collectResolutionMetrics;

      // Set up some stats
      const stats = (resolver as any)._resolutionStats;
      stats.totalResolutions = 10;
      stats.successfulResolutions = 8;
      stats.failedResolutions = 2;
      stats.conflictsResolved = 5;
      stats.averageResolutionTime = 150;
      stats.optimizationGains = 25;

      // Should not throw when collecting metrics
      expect(() => collectMethod.call(resolver)).not.toThrow();
    });

    it('should provide public utility methods', () => {
      // Test lines 727-743 - public utility methods
      const stats = resolver.getResolutionStatistics();
      expect(stats).toBeDefined();
      expect(typeof stats.totalResolutions).toBe('number');

      const initialCacheSize = resolver.getCacheSize();
      expect(typeof initialCacheSize).toBe('number');

      resolver.clearCache();
      const finalCacheSize = resolver.getCacheSize();
      expect(finalCacheSize).toBe(0);
    });
  });

  describe('Conflict Resolution Strategies', () => {
    beforeEach(async () => {
      resolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      await resolver.initialize();
    });

    it('should handle automatic conflict resolution', async () => {
      // Test lines 575-580 - automatic strategy
      const conflict = {
        conflictId: 'auto-conflict',
        type: 'version' as const,
        description: 'Automatic resolution test',
        affectedComponents: ['comp1'],
        resolutionStrategy: 'automatic'
      };

      // Clear the conflict strategies to test the null return path (line 580)
      const strategies = (resolver as any)._conflictStrategies;
      strategies.clear();

      const resolveMethod = (resolver as any)._resolveConflict;
      const result = await resolveMethod.call(resolver, conflict);

      // Should return null when no strategy is found (line 580)
      expect(result).toBeNull();
    });

    it('should handle manual conflict resolution strategy', async () => {
      // Test lines 635-637 - manual strategy
      const manualMethod = (resolver as any)._manualConflictResolution;
      const conflict = { conflictId: 'manual-test', type: 'circular' };

      const result = await manualMethod.call(resolver, conflict);
      expect(result).toBeDefined();
      expect(result.resolved).toBe(false);
      expect(result.strategy).toBe('manual');
      expect(result.requiresManualIntervention).toBe(true);
    });

    it('should handle fallback conflict resolution strategy', async () => {
      // Test lines 643-645 - fallback strategy
      const fallbackMethod = (resolver as any)._fallbackConflictResolution;
      const conflict = { conflictId: 'fallback-test', type: 'constraint' };

      const result = await fallbackMethod.call(resolver, conflict);
      expect(result).toBeDefined();
      expect(result.resolved).toBe(true);
      expect(result.strategy).toBe('fallback');
      expect(result.usedFallback).toBe(true);
    });

    it('should update resolution statistics correctly', async () => {
      // Test lines 651-660 - statistics update
      const updateMethod = (resolver as any)._updateResolutionStatistics;
      const stats = (resolver as any)._resolutionStats;

      const initialTotal = stats.totalResolutions;
      const initialSuccess = stats.successfulResolutions;
      const initialFailed = stats.failedResolutions;
      const initialConflicts = stats.conflictsResolved;

      // Test successful resolution
      const successResult = {
        status: 'success',
        conflicts: [{ id: 'c1' }, { id: 'c2' }],
        metrics: {
          resolutionTime: 100,
          successRate: 1.0,
          conflictCount: 2,
          optimizationGains: 15
        }
      };
      updateMethod.call(resolver, successResult);

      expect(stats.totalResolutions).toBe(initialTotal + 1);
      expect(stats.successfulResolutions).toBe(initialSuccess + 1);
      expect(stats.conflictsResolved).toBe(initialConflicts + 2);

      // Test failed resolution
      const failedResult = {
        status: 'failed',
        conflicts: [{ id: 'c3' }],
        metrics: {
          resolutionTime: 200,
          successRate: 0.0,
          conflictCount: 1,
          optimizationGains: 0
        }
      };
      updateMethod.call(resolver, failedResult);

      expect(stats.totalResolutions).toBe(initialTotal + 2);
      expect(stats.failedResolutions).toBe(initialFailed + 1);
      expect(stats.conflictsResolved).toBe(initialConflicts + 3);
    });
  });

  describe('Constructor and Initialization Edge Cases', () => {
    it('should handle resilient timing initialization failure', () => {
      // Test fallback timing initialization (lines around 230-231)
      const originalResilientTimer = (global as any).ResilientTimer;
      const originalResilientMetrics = (global as any).ResilientMetricsCollector;

      try {
        // Mock failure scenarios
        (global as any).ResilientTimer = jest.fn().mockImplementation(() => {
          throw new Error('ResilientTimer failed');
        });
        (global as any).ResilientMetricsCollector = jest.fn().mockImplementation(() => {
          throw new Error('ResilientMetricsCollector failed');
        });

        // Should still create resolver with fallback timing
        const testResolver = new ChainResolverEngine();
        expect(testResolver).toBeInstanceOf(ChainResolverEngine);

        // Clean up
        testResolver.shutdown();
      } finally {
        // Restore original implementations
        (global as any).ResilientTimer = originalResilientTimer;
        (global as any).ResilientMetricsCollector = originalResilientMetrics;
      }
    });

    it('should handle initialization errors in doInitialize', async () => {
      const testResolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);

      // Mock _enforceMemoryBoundaries to throw error
      const originalMethod = (testResolver as any)._enforceMemoryBoundaries;
      (testResolver as any)._enforceMemoryBoundaries = jest.fn().mockImplementation(() => {
        throw new Error('Memory boundary enforcement failed');
      });

      try {
        await expect(testResolver.initialize()).rejects.toThrow('Memory boundary enforcement failed');
      } finally {
        // Restore original method
        (testResolver as any)._enforceMemoryBoundaries = originalMethod;
        await testResolver.shutdown();
      }
    });
  });

  describe('Advanced Coverage Tests', () => {
    beforeEach(async () => {
      resolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      await resolver.initialize();
    });

    it('should handle doTrack method', async () => {
      // Test line 271 - doTrack implementation
      const trackMethod = (resolver as any).doTrack;
      const testData = { test: 'data', timestamp: Date.now() };

      // Should not throw when tracking data
      await expect(trackMethod.call(resolver, testData)).resolves.toBeUndefined();
    });

    it('should handle shutdown errors', async () => {
      // Test lines 336-337 - shutdown error handling
      const testResolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      await testResolver.initialize();

      // Mock _waitForActiveResolutions to throw error
      const originalMethod = (testResolver as any)._waitForActiveResolutions;
      (testResolver as any)._waitForActiveResolutions = jest.fn().mockImplementation(() => {
        throw new Error('Wait for active resolutions failed');
      });

      try {
        await expect(testResolver.shutdown()).rejects.toThrow('Wait for active resolutions failed');
      } finally {
        // Restore original method and force cleanup
        (testResolver as any)._waitForActiveResolutions = originalMethod;
        try {
          await testResolver.shutdown();
        } catch (e) {
          // Ignore cleanup errors
        }
      }
    });

    it('should wait for active resolutions during shutdown', async () => {
      // Test lines 671-674 - _waitForActiveResolutions
      const waitMethod = (resolver as any)._waitForActiveResolutions;

      // Add some active resolutions
      const activeResolutions = (resolver as any)._activeResolutions;
      const promise1 = Promise.resolve({ resolutionId: 'test1' });
      const promise2 = Promise.resolve({ resolutionId: 'test2' });
      activeResolutions.set('graph1', promise1);
      activeResolutions.set('graph2', promise2);

      // Should wait for all active resolutions
      await expect(waitMethod.call(resolver)).resolves.toBeUndefined();
    });

    it('should handle empty active resolutions during shutdown', async () => {
      // Test line 674 - empty active resolutions case
      const waitMethod = (resolver as any)._waitForActiveResolutions;

      // Clear active resolutions
      const activeResolutions = (resolver as any)._activeResolutions;
      activeResolutions.clear();

      // Should handle empty case gracefully
      await expect(waitMethod.call(resolver)).resolves.toBeUndefined();
    });

    it('should handle failed resolution status in statistics', async () => {
      // Test line 657 - failed resolution branch
      const updateMethod = (resolver as any)._updateResolutionStatistics;
      const stats = (resolver as any)._resolutionStats;

      const initialFailed = stats.failedResolutions;

      const failedResult = {
        status: 'failed',
        conflicts: [],
        metrics: {
          resolutionTime: 100,
          successRate: 0.0,
          conflictCount: 0,
          optimizationGains: 0
        }
      };

      updateMethod.call(resolver, failedResult);
      expect(stats.failedResolutions).toBe(initialFailed + 1);
    });

    it('should handle createSafeInterval in doInitialize', async () => {
      // Test line 305 - createSafeInterval call
      const testResolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);

      // Mock createSafeInterval to verify it's called
      const createSafeIntervalSpy = jest.spyOn(testResolver as any, 'createSafeInterval');

      await testResolver.initialize();

      // Verify createSafeInterval was called for metrics collection
      expect(createSafeIntervalSpy).toHaveBeenCalledWith(
        expect.any(Function),
        30000,
        'resolution-metrics-collection'
      );

      await testResolver.shutdown();
      createSafeIntervalSpy.mockRestore();
    });

    it('should handle successful conflict resolution', async () => {
      // Test line 437 - successful resolution branch
      const conflicts = [
        {
          conflictId: 'success-test',
          type: 'version' as const,
          description: 'Test successful resolution',
          affectedComponents: ['comp1'],
          resolutionStrategy: 'automatic'
        }
      ];

      // Mock _resolveConflict to return successful resolution
      const originalMethod = (resolver as any)._resolveConflict;
      (resolver as any)._resolveConflict = jest.fn().mockResolvedValue({
        resolved: true,
        strategy: 'automatic',
        conflict: conflicts[0]
      });

      try {
        const result = await resolver.resolveConflicts(conflicts);
        expect(result).toBeDefined();
        expect(result.resolvedChains).toBeDefined();
      } finally {
        // Restore original method
        (resolver as any)._resolveConflict = originalMethod;
      }
    });

    it('should handle resilient timing initialization with specific configuration', () => {
      // Test lines 230-231 - specific resilient timing configuration
      const testResolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);

      // Verify resilient timing components are initialized
      expect((testResolver as any)._resilientTimer).toBeDefined();
      expect((testResolver as any)._metricsCollector).toBeDefined();

      testResolver.shutdown();
    });

    it('should trigger metrics collection directly', async () => {
      // Test line 305 - metrics collection function execution
      const testResolver = new ChainResolverEngine(mockTrackingConfig, mockResolverConfig);
      await testResolver.initialize();

      // Directly call the metrics collection method to test line 305
      const collectMethod = (testResolver as any)._collectResolutionMetrics;

      // Should not throw when collecting metrics
      expect(() => collectMethod.call(testResolver)).not.toThrow();

      await testResolver.shutdown();
    });
  });
});
