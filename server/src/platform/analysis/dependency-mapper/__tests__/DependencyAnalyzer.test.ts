/**
 * ============================================================================
 * OA FRAMEWORK - M0.1 ENTERPRISE ENHANCEMENT IMPLEMENTATION
 * Dependency Analyzer Test Suite
 * ============================================================================
 * 
 * @fileoverview Comprehensive test suite for DependencyAnalyzer
 * @version 2.3.0
 * @since 2025-09-17
 * <AUTHOR> & CEO, <PERSON><PERSON>Z. Consultancy
 * 
 * 🎯 M0.1 ENTERPRISE ENHANCEMENT TASK: ENH-TSK-01.SUB-01.1.IMP-04
 * 📋 COMPONENT: Dependency Analyzer - Analysis Engine
 * 🔧 REFACTOR: REF-02 - Analysis capabilities (584 LOC)
 * 
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level enterprise-grade
 * @base-class BaseTrackingService
 * @memory-boundaries enforced
 * @resource-cleanup automatic
 * @timing-resilience circuit-breaker-enabled
 * @performance-target <12ms
 * @memory-footprint <40MB
 * @resilient-timing-integration dual-field-pattern
 * @memory-leak-prevention comprehensive
 * @resource-monitoring continuous
 * 
 * 📊 TESTING COVERAGE TARGETS
 * @line-coverage ≥95%
 * @branch-coverage ≥75%
 * @function-coverage 100%
 * @statement-coverage ≥95%
 * 
 * ============================================================================
 */

import { DependencyAnalyzer } from '../DependencyAnalyzer';
import { TDependencyMapperConfig, TDependencyAnalysisResult } from '../../../../../../shared/src/types/platform/analysis/dependency-mapper-types';
import { TTrackingConfig } from '../../../../../../shared/src/types/platform/tracking/tracking-types';

describe('DependencyAnalyzer', () => {
  let analyzer: DependencyAnalyzer;
  let mockAnalyzerConfig: { maxConcurrentAnalyses: number; analysisTimeout: number; cacheAnalysisResults: boolean; enableTrendAnalysis: boolean; riskThresholds: { low: number; medium: number; high: number; critical: number; }; };
  let mockTrackingConfig: TTrackingConfig;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockAnalyzerConfig = {
      maxConcurrentAnalyses: 3,
      analysisTimeout: 45000,
      cacheAnalysisResults: true,
      enableTrendAnalysis: true,
      riskThresholds: {
        low: 0.3,
        medium: 0.6,
        high: 0.8,
        critical: 0.9
      }
    };

    mockTrackingConfig = {
      service: {
        name: 'DependencyAnalyzer',
        version: '1.0.0',
        environment: 'development',
        timeout: 45000,
        retry: {
          maxAttempts: 3,
          delay: 1000,
          backoffMultiplier: 2,
          maxDelay: 10000
        }
      },
      governance: {
        authority: 'President & CEO, E.Z. Consultancy',
        requiredCompliance: ['authority-validation', 'dependency-analysis', 'enterprise-enhancement'],
        auditFrequency: 24,
        violationReporting: true
      },
      performance: {
        metricsEnabled: true,
        metricsInterval: 60000,
        monitoringEnabled: true,
        alertThresholds: {
          responseTime: 12000,
          errorRate: 5,
          memoryUsage: 4096,
          cpuUsage: 80
        }
      },
      logging: {
        level: 'info',
        format: 'json',
        rotation: true,
        maxFileSize: 10
      }
    };
  });

  afterEach(async () => {
    if (analyzer) {
      await analyzer.shutdown();
    }
  });

  describe('Constructor and Initialization', () => {
    it('should initialize with default configuration', () => {
      analyzer = new DependencyAnalyzer();
      expect(analyzer).toBeInstanceOf(DependencyAnalyzer);
    });

    it('should initialize with custom configuration', () => {
      analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
      expect(analyzer).toBeInstanceOf(DependencyAnalyzer);
    });

    it('should initialize resilient timing infrastructure', () => {
      analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
      expect((analyzer as any)._resilientTimer).toBeDefined();
      expect((analyzer as any)._metricsCollector).toBeDefined();
    });
  });

  describe('BaseTrackingService Integration', () => {
    beforeEach(() => {
      analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
    });

    it('should implement required abstract methods', () => {
      expect((analyzer as any).getServiceName()).toBe('DependencyAnalyzer');
      expect((analyzer as any).getServiceVersion()).toBe('1.0.0');
    });

    it('should handle initialization lifecycle', async () => {
      await analyzer.initialize();
      expect((analyzer as any)._isInitialized).toBe(true);
    });

    it('should handle shutdown lifecycle', async () => {
      await analyzer.initialize();
      await analyzer.shutdown();
      expect((analyzer as any)._isInitialized).toBe(false);
    });

    it('should track operations', async () => {
      await analyzer.initialize();
      // Test that track method exists and can be called
      expect(typeof analyzer.track).toBe('function');
    });

    it('should validate operations', async () => {
      await analyzer.initialize();
      const validationResult = await analyzer.validate();
      expect(validationResult).toBeDefined();
    });
  });

  describe('IDependencyAnalyzer Interface Implementation', () => {
    beforeEach(async () => {
      analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
      await analyzer.initialize();
    });

    it('should analyze dependencies with impact analysis', async () => {
      const graphId = 'test-graph';
      const result = await analyzer.analyzeDependencies(graphId, 'impact');
      
      expect(result).toBeDefined();
      expect(result.analysisType).toBe('impact');
      expect(result.results.summary).toBeDefined();
      expect(result.results.findings).toBeDefined();
      expect(result.metadata).toBeDefined();
    });

    it('should analyze dependencies with performance analysis', async () => {
      const graphId = 'test-graph';
      const result = await analyzer.analyzeDependencies(graphId, 'performance');
      
      expect(result).toBeDefined();
      expect(result.analysisType).toBe('performance');
      expect(result.results.summary).toBeDefined();
    });

    it('should analyze dependencies with risk analysis', async () => {
      const graphId = 'test-graph';
      const result = await analyzer.analyzeDependencies(graphId, 'risk');
      
      expect(result).toBeDefined();
      expect(result.analysisType).toBe('risk');
    });

    it('should analyze dependencies with optimization analysis', async () => {
      const graphId = 'test-graph';
      const result = await analyzer.analyzeDependencies(graphId, 'optimization');
      
      expect(result).toBeDefined();
      expect(result.analysisType).toBe('optimization');
    });

    it('should detect circular dependencies', async () => {
      const graphId = 'test-graph';
      const result = await analyzer.detectCircularDependencies(graphId);
      
      expect(Array.isArray(result)).toBe(true);
    });

    it('should calculate dependency impact', async () => {
      const componentId = 'test-component';
      const result = await analyzer.calculateDependencyImpact(componentId);
      
      expect(typeof result).toBe('number');
      expect(result).toBeGreaterThanOrEqual(0);
      expect(result).toBeLessThanOrEqual(100);
    });

    it('should generate optimization recommendations', async () => {
      const graphId = 'test-graph';
      const result = await analyzer.generateOptimizationRecommendations(graphId);
      
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe('Analysis Types and Methods', () => {
    beforeEach(async () => {
      analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
      await analyzer.initialize();
    });

    it('should perform impact analysis', async () => {
      const graphId = 'impact-test-graph';
      // Access private method for testing
      const performDependencyAnalysis = (analyzer as any)._performDependencyAnalysis;
      const result = await performDependencyAnalysis.call(analyzer, graphId, 'impact');

      expect(result).toBeDefined();
      expect(result.results.summary).toBeDefined();
    });

    it('should perform performance analysis', async () => {
      const graphId = 'performance-test-graph';
      // Access private method for testing
      const performDependencyAnalysis = (analyzer as any)._performDependencyAnalysis;
      const result = await performDependencyAnalysis.call(analyzer, graphId, 'performance');

      expect(result).toBeDefined();
      expect(result.results.summary).toBeDefined();
    });

    it('should perform risk analysis', async () => {
      const graphId = 'risk-test-graph';
      // Access private method for testing
      const performDependencyAnalysis = (analyzer as any)._performDependencyAnalysis;
      const result = await performDependencyAnalysis.call(analyzer, graphId, 'risk');

      expect(result).toBeDefined();
      expect(result.results.summary).toBeDefined();
    });

    it('should perform optimization analysis', async () => {
      const graphId = 'optimization-test-graph';
      // Access private method for testing
      const performDependencyAnalysis = (analyzer as any)._performDependencyAnalysis;
      const result = await performDependencyAnalysis.call(analyzer, graphId, 'optimization');

      expect(result).toBeDefined();
      expect(result.results.summary).toBeDefined();
    });

    it('should detect cycles in dependency graph', async () => {
      const graphId = 'cycle-test-graph';
      // Access private method for testing
      const detectCycles = (analyzer as any)._detectCycles;
      const result = await detectCycles.call(analyzer, graphId);
      
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe('Performance and Memory Safety', () => {
    beforeEach(async () => {
      analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
      await analyzer.initialize();
    });

    it('should complete operations within performance targets', async () => {
      const startTime = Date.now();
      await analyzer.analyzeDependencies('test-graph', 'impact');
      const duration = Date.now() - startTime;
      
      expect(duration).toBeLessThan(12); // <12ms target for analyzer
    });

    it('should enforce memory boundaries', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform multiple analyses
      const analysisTypes: Array<TDependencyAnalysisResult['analysisType']> = ['impact', 'performance', 'risk', 'optimization'];
      for (let i = 0; i < 10; i++) {
        const analysisType = analysisTypes[i % analysisTypes.length];
        await analyzer.analyzeDependencies(`graph-${i}`, analysisType);
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024; // MB
      
      expect(memoryIncrease).toBeLessThan(40); // <40MB target for analyzer
    });

    it('should handle concurrent analyses', async () => {
      const operations = Array.from({ length: 3 }, (_, i) => 
        analyzer.analyzeDependencies(`concurrent-graph-${i}`, 'impact')
      );
      
      await expect(Promise.all(operations)).resolves.toBeDefined();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    beforeEach(async () => {
      analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
      await analyzer.initialize();
    });

    it('should handle invalid graph IDs', async () => {
      await expect(analyzer.analyzeDependencies('', 'impact')).rejects.toThrow();
      await expect(analyzer.analyzeDependencies(null as any, 'impact')).rejects.toThrow();
    });

    it('should handle invalid analysis types', async () => {
      const graphId = 'test-graph';
      await expect(analyzer.analyzeDependencies(graphId, 'invalid' as any)).rejects.toThrow();
    });

    it('should handle invalid component IDs for impact calculation', async () => {
      await expect(analyzer.calculateDependencyImpact('')).rejects.toThrow();
      await expect(analyzer.calculateDependencyImpact(null as any)).rejects.toThrow();
    });

    it('should handle empty graphs gracefully', async () => {
      const graphId = 'empty-graph';
      const result = await analyzer.detectCircularDependencies(graphId);
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
    });
  });

  describe('Analysis Configuration', () => {
    beforeEach(async () => {
      analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
      await analyzer.initialize();
    });

    it('should respect analysis configuration flags', async () => {
      // Test with impact analysis disabled
      const disabledConfig = {
        ...mockAnalyzerConfig,
        cacheAnalysisResults: false
      };

      const disabledAnalyzer = new DependencyAnalyzer(mockTrackingConfig, disabledConfig);
      await disabledAnalyzer.initialize();
      
      // Should still work but may have different behavior
      const result = await disabledAnalyzer.analyzeDependencies('test-graph', 'impact');
      expect(result).toBeDefined();
      
      await disabledAnalyzer.shutdown();
    });

    it('should handle all analysis types being disabled', async () => {
      const allDisabledConfig = {
        ...mockAnalyzerConfig,
        cacheAnalysisResults: false,
        enableTrendAnalysis: false
      };

      const disabledAnalyzer = new DependencyAnalyzer(mockTrackingConfig, allDisabledConfig);
      await disabledAnalyzer.initialize();
      
      // Should still work with basic functionality
      const result = await disabledAnalyzer.detectCircularDependencies('test-graph');
      expect(Array.isArray(result)).toBe(true);
      
      await disabledAnalyzer.shutdown();
    });
  });

  // ============================================================================
  // SURGICAL PRECISION TESTING - COVERAGE ENHANCEMENT
  // ============================================================================

  describe('Surgical Precision Testing - Coverage Enhancement', () => {
    describe('Uncovered Line Coverage - Direct Method Testing', () => {
      test('should cover comprehensive analysis type (lines 563-564)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Test the comprehensive analysis type that was uncovered
        const result = await analyzer.analyzeDependencies('test-graph-001', 'comprehensive');

        expect(result).toBeDefined();
        expect(result.analysisType).toBe('comprehensive');
        expect(result.results).toBeDefined();
        expect(result.results.summary).toBeDefined();
        expect(result.results.summary.totalDependencies).toBe(50);
        expect(result.results.summary.criticalDependencies).toBe(6);

        await analyzer.shutdown();
      });

      test('should cover _performComprehensiveAnalysis method (lines 704-709)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Direct access to private method for surgical precision testing
        const comprehensiveAnalysisMethod = (analyzer as any)._performComprehensiveAnalysis.bind(analyzer);
        const result = await comprehensiveAnalysisMethod('test-graph-001');

        expect(result).toBeDefined();
        expect(result.summary).toBeDefined();
        expect(result.summary.totalDependencies).toBe(50);
        expect(result.summary.criticalDependencies).toBe(6);
        expect(result.findings).toBeDefined();
        expect(Array.isArray(result.findings)).toBe(true);

        await analyzer.shutdown();
      });

      test('should cover _waitForActiveAnalyses method (line 778)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Add some active analyses to the map
        const mockPromise1 = Promise.resolve({ analysisType: 'impact', summary: {} });
        const mockPromise2 = Promise.resolve({ analysisType: 'performance', summary: {} });
        (analyzer as any)._activeAnalyses.set('analysis-1', mockPromise1);
        (analyzer as any)._activeAnalyses.set('analysis-2', mockPromise2);

        // Direct access to private method for surgical precision testing
        const waitForActiveAnalysesMethod = (analyzer as any)._waitForActiveAnalyses.bind(analyzer);
        await waitForActiveAnalysesMethod();

        // Verify the method completed successfully
        expect((analyzer as any)._activeAnalyses.size).toBe(2);

        await analyzer.shutdown();
      });

      test('should cover memory boundary enforcement (lines 789-791, 796-829)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Fill caches beyond the limit to trigger memory boundary enforcement
        const maxCacheSize = 500;
        for (let i = 0; i <= maxCacheSize + 10; i++) {
          (analyzer as any)._analysisCache.set(`key-${i}`, {
            analysisType: 'test',
            summary: { id: i }
          });
          (analyzer as any)._riskAssessmentCache.set(`risk-${i}`, 0.5);
        }

        // Direct access to private method for surgical precision testing
        const enforceMemoryBoundariesMethod = (analyzer as any)._enforceMemoryBoundaries.bind(analyzer);
        enforceMemoryBoundariesMethod();

        // Verify memory boundaries were enforced
        expect((analyzer as any)._analysisCache.size).toBeLessThanOrEqual(maxCacheSize);
        expect((analyzer as any)._riskAssessmentCache.size).toBeLessThanOrEqual(maxCacheSize);

        await analyzer.shutdown();
      });

      test('should cover analysis metrics collection (lines 806-822)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Set up analysis stats to trigger metrics collection
        (analyzer as any)._analysisStats.totalAnalyses = 5;
        (analyzer as any)._analysisStats.lastAnalysisTime = Date.now() - 1000;
        (analyzer as any)._analysisCache.set('test-1', { analysisType: 'test' });
        (analyzer as any)._analysisCache.set('test-2', { analysisType: 'test' });

        // Direct access to private method for surgical precision testing
        const collectAnalysisMetricsMethod = (analyzer as any)._collectAnalysisMetrics.bind(analyzer);
        collectAnalysisMetricsMethod();

        // Verify metrics were collected
        expect((analyzer as any)._analysisStats.averageAnalysisTime).toBeGreaterThan(0);
        expect((analyzer as any)._analysisStats.cacheHitRate).toBeGreaterThan(0);

        await analyzer.shutdown();
      });

      test('should cover cache cleanup method (lines 827-833)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Add some cache entries
        (analyzer as any)._analysisCache.set('test-1', { analysisType: 'test' });
        (analyzer as any)._riskAssessmentCache.set('risk-1', 0.5);

        // Direct access to private method for surgical precision testing
        const cleanupAnalysisCacheMethod = (analyzer as any)._cleanupAnalysisCache.bind(analyzer);
        cleanupAnalysisCacheMethod();

        // Verify the method executed (it logs but doesn't modify cache in current implementation)
        expect((analyzer as any)._analysisCache.size).toBeGreaterThanOrEqual(0);

        await analyzer.shutdown();
      });
    });

    describe('Public Method Coverage - Statistics and Cache Management', () => {
      test('should cover getAnalysisStatistics method (lines 850-854)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Set up some analysis statistics
        (analyzer as any)._analysisStats.totalAnalyses = 10;
        (analyzer as any)._analysisStats.averageAnalysisTime = 150;
        (analyzer as any)._analysisStats.cacheHitRate = 0.75;
        (analyzer as any)._analysisStats.analysisTypeDistribution.set('impact', 5);
        (analyzer as any)._analysisStats.analysisTypeDistribution.set('performance', 3);

        const stats = analyzer.getAnalysisStatistics();

        expect(stats.totalAnalyses).toBe(10);
        expect(stats.averageAnalysisTime).toBe(150);
        expect(stats.cacheHitRate).toBe(0.75);
        expect(stats.analysisTypeDistribution).toBeInstanceOf(Map);
        expect(stats.analysisTypeDistribution.get('impact')).toBe(5);
        expect(stats.analysisTypeDistribution.get('performance')).toBe(3);

        await analyzer.shutdown();
      });

      test('should cover clearCaches method (lines 859-863)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Add some cache entries
        (analyzer as any)._analysisCache.set('test-1', { analysisType: 'test' });
        (analyzer as any)._riskAssessmentCache.set('risk-1', 0.5);

        // Verify caches have entries
        expect((analyzer as any)._analysisCache.size).toBe(1);
        expect((analyzer as any)._riskAssessmentCache.size).toBe(1);

        // Clear caches
        analyzer.clearCaches();

        // Verify caches are cleared
        expect((analyzer as any)._analysisCache.size).toBe(0);
        expect((analyzer as any)._riskAssessmentCache.size).toBe(0);

        await analyzer.shutdown();
      });

      test('should cover getCacheSizes method (lines 868-878)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Add some cache entries and active analyses
        (analyzer as any)._analysisCache.set('test-1', { analysisType: 'test' });
        (analyzer as any)._analysisCache.set('test-2', { analysisType: 'test' });
        (analyzer as any)._riskAssessmentCache.set('risk-1', 0.5);
        (analyzer as any)._activeAnalyses.set('active-1', Promise.resolve({}));

        const cacheSizes = analyzer.getCacheSizes();

        expect(cacheSizes.analysisCache).toBe(2);
        expect(cacheSizes.riskAssessmentCache).toBe(1);
        expect(cacheSizes.activeAnalyses).toBe(1);

        await analyzer.shutdown();
      });
    });

    describe('Error Injection and Edge Case Testing', () => {
      test('should handle errors in comprehensive analysis gracefully', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Mock one of the internal analysis methods to throw an error
        const originalImpactAnalysis = (analyzer as any)._performImpactAnalysis;
        (analyzer as any)._performImpactAnalysis = jest.fn().mockImplementation(() => {
          throw new Error('Impact analysis service unavailable');
        });

        try {
          // This should handle the error gracefully
          await expect(analyzer.analyzeDependencies('test-graph-001', 'comprehensive'))
            .rejects.toThrow('Impact analysis service unavailable');
        } finally {
          // Restore original method
          (analyzer as any)._performImpactAnalysis = originalImpactAnalysis;
          await analyzer.shutdown();
        }
      });

      test('should handle resilient timing integration errors', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Mock resilient timer to return a context that works but logs error
        const originalTimer = (analyzer as any)._resilientTimer;
        const mockContext = { end: jest.fn().mockReturnValue({ duration: 5 }) };
        (analyzer as any)._resilientTimer = {
          start: jest.fn().mockReturnValue(mockContext)
        };

        try {
          // Should handle timer integration successfully
          const result = await analyzer.analyzeDependencies('test-graph-001', 'impact');
          expect(result).toBeDefined();
          expect(mockContext.end).toHaveBeenCalled();
        } finally {
          // Restore original timer
          (analyzer as any)._resilientTimer = originalTimer;
          await analyzer.shutdown();
        }
      });

      test('should handle metrics collector errors', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Mock metrics collector with proper interface
        const originalCollector = (analyzer as any)._metricsCollector;
        (analyzer as any)._metricsCollector = {
          recordTiming: jest.fn().mockImplementation(() => {
            throw new Error('Metrics service unavailable');
          }),
          recordValue: jest.fn()
        };

        try {
          // Should handle metrics errors gracefully by catching them
          await expect(analyzer.analyzeDependencies('test-graph-001', 'performance'))
            .rejects.toThrow('Metrics service unavailable');
        } finally {
          // Restore original collector
          (analyzer as any)._metricsCollector = originalCollector;
          await analyzer.shutdown();
        }
      });

      test('should handle concurrent analysis limits', async () => {
        const limitedConfig = {
          ...mockAnalyzerConfig,
          maxConcurrentAnalyses: 1
        };
        analyzer = new DependencyAnalyzer(mockTrackingConfig, limitedConfig);
        await analyzer.initialize();

        // Start multiple analyses to test concurrency limits
        const promises = [
          analyzer.analyzeDependencies('graph-1', 'impact'),
          analyzer.analyzeDependencies('graph-2', 'performance'),
          analyzer.analyzeDependencies('graph-3', 'risk')
        ];

        const results = await Promise.all(promises);
        expect(results).toHaveLength(3);
        results.forEach(result => {
          expect(result).toBeDefined();
          expect(result.analysisType).toBeDefined();
        });

        await analyzer.shutdown();
      });
    });

    describe('Resilient Timing Integration Coverage', () => {
      test('should verify resilient timing dual-field pattern', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Verify dual-field pattern exists
        expect((analyzer as any)._resilientTimer).toBeDefined();
        expect((analyzer as any)._metricsCollector).toBeDefined();

        // Verify timing integration works
        const result = await analyzer.analyzeDependencies('test-graph-001', 'impact');
        expect(result).toBeDefined();

        await analyzer.shutdown();
      });

      test('should handle timing context errors gracefully', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Mock timing context that throws error on end()
        const mockContext = {
          end: jest.fn().mockImplementation(() => {
            throw new Error('Timer end failed');
          })
        };
        const originalTimer = (analyzer as any)._resilientTimer;
        (analyzer as any)._resilientTimer = {
          start: jest.fn().mockReturnValue(mockContext)
        };

        try {
          // Should fail due to timer end() error but we can catch it
          await expect(analyzer.analyzeDependencies('test-graph-001', 'impact'))
            .rejects.toThrow('Timer end failed');

          // Verify timing context end() was called (even though it threw)
          expect(mockContext.end).toHaveBeenCalled();
        } finally {
          (analyzer as any)._resilientTimer = originalTimer;
          await analyzer.shutdown();
        }
      });
    });

    describe('Advanced Coverage Enhancement - Remaining Lines', () => {
      test('should cover cache hit path in analyzeDependencies (lines 368, 373-374)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // First call to populate cache
        const result1 = await analyzer.analyzeDependencies('test-graph-cache', 'impact');
        expect(result1).toBeDefined();

        // Second call should hit cache (lines 368, 373-374)
        const result2 = await analyzer.analyzeDependencies('test-graph-cache', 'impact');
        expect(result2).toBeDefined();
        expect(result2.analysisId).toBe(result1.analysisId);

        await analyzer.shutdown();
      });

      test('should cover active analysis awaiting path (lines 373-374)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Disable caching to avoid cache hit path
        (analyzer as any)._analyzerConfig.cacheAnalysisResults = false;

        // Start an analysis but don't await it immediately
        const promise1 = analyzer.analyzeDependencies('test-graph-active', 'impact');

        // Start another analysis with same parameters - should await the first one
        const promise2 = analyzer.analyzeDependencies('test-graph-active', 'impact');

        const [result1, result2] = await Promise.all([promise1, promise2]);
        expect(result1).toBeDefined();
        expect(result2).toBeDefined();

        await analyzer.shutdown();
      });

      test('should cover error handling in detectCircularDependencies (lines 441-445)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Mock _detectCycles to throw an error
        const originalDetectCycles = (analyzer as any)._detectCycles;
        (analyzer as any)._detectCycles = jest.fn().mockImplementation(() => {
          throw new Error('Cycle detection failed');
        });

        try {
          await expect(analyzer.detectCircularDependencies('test-graph-error'))
            .rejects.toThrow('Cycle detection failed');
        } finally {
          // Restore original method
          (analyzer as any)._detectCycles = originalDetectCycles;
          await analyzer.shutdown();
        }
      });

      test('should cover cache hit path in calculateDependencyImpact (lines 468-470)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // First call to populate cache
        const impact1 = await analyzer.calculateDependencyImpact('component-cache-test');
        expect(typeof impact1).toBe('number');

        // Second call should hit cache (lines 468-470)
        const impact2 = await analyzer.calculateDependencyImpact('component-cache-test');
        expect(impact2).toBe(impact1);

        await analyzer.shutdown();
      });

      test('should cover metrics recording in detectCircularDependencies (line 448)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Mock metrics collector to verify it's called
        const recordTimingSpy = jest.spyOn((analyzer as any)._metricsCollector, 'recordTiming');

        const result = await analyzer.detectCircularDependencies('test-graph-metrics');
        expect(result).toBeDefined();
        expect(recordTimingSpy).toHaveBeenCalledWith('detectCircularDependencies', expect.any(Object));

        recordTimingSpy.mockRestore();
        await analyzer.shutdown();
      });

      test('should cover error path in calculateDependencyImpact (lines 509, 522-526)', async () => {
        analyzer = new DependencyAnalyzer(mockTrackingConfig, mockAnalyzerConfig);
        await analyzer.initialize();

        // Mock _calculateImpactScore to throw an error
        const originalCalculateImpact = (analyzer as any)._calculateImpactScore;
        (analyzer as any)._calculateImpactScore = jest.fn().mockImplementation(() => {
          throw new Error('Impact calculation failed');
        });

        try {
          await expect(analyzer.calculateDependencyImpact('component-error-test'))
            .rejects.toThrow('Impact calculation failed');
        } finally {
          // Restore original method
          (analyzer as any)._calculateImpactScore = originalCalculateImpact;
          await analyzer.shutdown();
        }
      });
    });
  });
});
