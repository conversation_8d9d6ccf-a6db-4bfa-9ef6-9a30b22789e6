/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT TEST FILE (v2.3)
 * ============================================================================
 *
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * 🧪 TEST CONTEXT: Documentation Formatter - Comprehensive Test Suite
 * Purpose: Enterprise-grade testing with 95%+ coverage, MEM-SAFE-002 compliance, and resilient timing validation
 * Complexity: High - Comprehensive testing with surgical precision techniques
 * Test Coverage Target: 95%+ line coverage, 100% branch coverage
 * Performance Validation: <10ms response time requirements
 * Memory Safety: MEM-SAFE-002 compliance testing
 * Resilient Timing: Dual-field pattern validation
 */

import { jest } from '@jest/globals';
import { DocumentationFormatter, TFormattedOutput } from '../DocumentationFormatter';

// Mock dependencies with proper ResilientTimer interface
jest.mock('../../../../../../shared/src/base/utils/ResilientTiming', () => ({
  ResilientTimer: jest.fn().mockImplementation(() => ({
    start: jest.fn(() => ({
      end: jest.fn(() => ({
        duration: 5,
        reliable: true,
        fallbackUsed: false,
        timestamp: Date.now(),
        method: 'date' as const
      }))
    }))
  }))
}));

jest.mock('../../../../../../shared/src/base/utils/ResilientMetrics', () => ({
  ResilientMetricsCollector: jest.fn().mockImplementation(() => ({
    recordValue: jest.fn(),
    getMetric: jest.fn(() => ({ value: 0 })),
    reset: jest.fn(),
    getMetrics: jest.fn(() => ({}))
  }))
}));

describe('DocumentationFormatter', () => {
  let formatter: DocumentationFormatter;

  beforeEach(() => {
    jest.clearAllMocks();
    formatter = new DocumentationFormatter();
  });

  afterEach(async () => {
    if (formatter) {
      await formatter.shutdown();
    }
  });

  describe('Initialization and Configuration', () => {
    test('should initialize with default configuration', () => {
      expect(formatter).toBeInstanceOf(DocumentationFormatter);
    });

    test('should initialize with custom configuration', () => {
      const customConfig = {
        service: {
          name: 'custom-documentation-formatter',
          version: '2.0.0',
          environment: 'development' as const,
          timeout: 30000,
          retry: {
            maxAttempts: 3,
            delay: 1000,
            backoffMultiplier: 2,
            maxDelay: 5000
          }
        },
        governance: {
          authority: 'Test Authority',
          requiredCompliance: ['test-compliance'],
          auditFrequency: 24,
          violationReporting: true
        },
        performance: {
          metricsEnabled: true,
          metricsInterval: 60000,
          monitoringEnabled: true,
          alertThresholds: {
            cpuUsage: 80,
            memoryUsage: 70,
            responseTime: 5000,
            errorRate: 5
          }
        },
        logging: {
          level: 'info' as const,
          format: 'json' as const,
          rotation: true,
          maxFileSize: 100
        }
      };

      const customFormatter = new DocumentationFormatter(customConfig);
      expect(customFormatter).toBeInstanceOf(DocumentationFormatter);
    });

    test('should initialize resilient timing infrastructure', () => {
      // Verify dual-field pattern implementation
      expect((formatter as any)._resilientTimer).toBeDefined();
      expect((formatter as any)._metricsCollector).toBeDefined();
    });
  });

  describe('Service Lifecycle Management', () => {
    test('should initialize service successfully', async () => {
      await formatter.initialize();
      expect((formatter as any)._isInitialized).toBe(true);
    });

    test('should validate service state correctly', async () => {
      await formatter.initialize();
      const result = await formatter.validate();
      expect(result).toMatchObject({
        componentId: 'documentation-formatter',
        status: 'valid'
      });
    });

    test('should shutdown service cleanly', async () => {
      await formatter.initialize();
      await formatter.shutdown();
      expect((formatter as any)._isInitialized).toBe(false);
      expect(formatter.isReady()).toBe(false);
    });
  });

  describe('Documentation Formatting', () => {
    const mockDocumentationContent = {
      sections: [{
        id: 'overview',
        title: 'Overview',
        content: 'This is an overview section',
        subsections: [],
        order: 1,
        metadata: {}
      }],
      tableOfContents: [{
        id: 'overview',
        title: 'Overview',
        level: 1,
        reference: '#overview',
        children: []
      }],
      metadata: {
        generatedAt: '2025-09-16T16:00:00Z',
        generatedBy: 'DocumentationGeneratorCore',
        version: '1.0.0',
        apiVersion: '1.0.0',
        totalSections: 1,
        estimatedReadingTime: 1,
        generationTime: 5,
        templateUsed: 'markdown'
      },
      appendices: []
    };

    test('should format documentation as markdown successfully', async () => {
      const options = {
        format: 'markdown' as const,
        includeNavigation: true,
        includeSearch: false,
        responsiveDesign: false,
        accessibility: false
      };

      const result = await formatter.formatDocumentation(mockDocumentationContent, options);

      expect(result).toMatchObject({
        id: expect.any(String),
        format: 'markdown',
        content: expect.any(String),
        metadata: expect.any(Object),
        assets: expect.any(Array),
        performance: expect.any(Object)
      });

      expect(result.content).toContain('## Table of Contents');
      expect(result.content).toContain('## Overview');
    });

    test('should format documentation as HTML successfully', async () => {
      const options = {
        format: 'html' as const,
        includeNavigation: true,
        includeSearch: true,
        responsiveDesign: true,
        accessibility: true,
        customStyles: {
          'body': 'font-family: Arial, sans-serif;'
        }
      };

      const result = await formatter.formatDocumentation(mockDocumentationContent, options);

      expect(result.format).toBe('html');
      expect(result.content).toContain('<!DOCTYPE html>');
      expect(result.content).toContain('<nav>');
      expect(result.content).toContain('<section id="overview">');
      expect(result.assets).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ type: 'css' }),
          expect.objectContaining({ type: 'js' })
        ])
      );
    });

    test('should format documentation as JSON successfully', async () => {
      const options = {
        format: 'json' as const,
        includeNavigation: false,
        includeSearch: false,
        responsiveDesign: false,
        accessibility: false
      };

      const result = await formatter.formatDocumentation(mockDocumentationContent, options);

      expect(result.format).toBe('json');
      expect(() => JSON.parse(result.content)).not.toThrow();
      
      const parsedContent = JSON.parse(result.content);
      expect(parsedContent).toMatchObject(mockDocumentationContent);
    });

    test('should handle PDF format gracefully', async () => {
      const options = {
        format: 'pdf' as const,
        includeNavigation: false,
        includeSearch: false,
        responsiveDesign: false,
        accessibility: false
      };

      const result = await formatter.formatDocumentation(mockDocumentationContent, options);

      expect(result.format).toBe('pdf');
      expect(result.content).toContain('PDF generation not implemented');
    });

    test('should utilize caching effectively', async () => {
      const options = {
        format: 'markdown' as const,
        includeNavigation: true,
        includeSearch: false,
        responsiveDesign: false,
        accessibility: false
      };

      // First formatting
      const result1 = await formatter.formatDocumentation(mockDocumentationContent, options);
      
      // Second formatting (should hit cache)
      const result2 = await formatter.formatDocumentation(mockDocumentationContent, options);

      expect(result1.id).toBe(result2.id);
    });
  });

  describe('Output Processing', () => {
    const mockFormattedOutput = {
      id: 'test-output-id',
      format: 'html',
      content: '<html><body><h1>Test</h1><p>Content with   extra   spaces</p></body></html>',
      metadata: {
        formattedAt: '2025-09-16T16:00:00Z',
        formattedBy: 'DocumentationFormatter',
        format: 'html',
        size: 100,
        optimizations: [],
        performance: {
          formattingTime: 5,
          memoryUsage: 1024,
          optimizationTime: 0
        }
      },
      assets: [],
      performance: {
        formattingTime: 5,
        memoryUsage: 1024,
        optimizationTime: 0
      }
    };

    test('should process output with minification', async () => {
      const configuration = {
        minify: true,
        compress: false,
        includeSourceMaps: false,
        outputEncoding: 'utf8' as const,
        metadata: false
      };

      const result = await formatter.processOutput(mockFormattedOutput, configuration);

      expect(result).not.toContain('   '); // Extra spaces should be removed
      expect(result.length).toBeLessThan(mockFormattedOutput.content.length);
    });

    test('should process output with compression', async () => {
      const configuration = {
        minify: false,
        compress: true,
        includeSourceMaps: false,
        outputEncoding: 'utf8' as const,
        metadata: false
      };

      const result = await formatter.processOutput(mockFormattedOutput, configuration);

      expect(result).toBeDefined();
      // Note: Compression is placeholder implementation
    });

    test('should process output with metadata', async () => {
      const configuration = {
        minify: false,
        compress: false,
        includeSourceMaps: false,
        outputEncoding: 'utf8' as const,
        metadata: true
      };

      const result = await formatter.processOutput(mockFormattedOutput, configuration);

      expect(result).toContain('<!-- Generated by DocumentationFormatter');
      expect(result).toContain('2025-09-16T16:00:00Z');
    });
  });

  describe('Content Optimization', () => {
    test('should optimize content with minification', async () => {
      const content = '<html>  <body>  <h1>Test</h1>  </body>  </html>';
      const optimizations = [{
        type: 'minify' as const,
        enabled: true
      }];

      const result = await formatter.optimizeContent(content, optimizations);

      expect(result).not.toContain('  '); // Extra spaces should be removed
      expect(result.length).toBeLessThan(content.length);
    });

    test('should optimize content with compression', async () => {
      const content = 'This is test content for compression';
      const optimizations = [{
        type: 'compress' as const,
        enabled: true
      }];

      const result = await formatter.optimizeContent(content, optimizations);

      expect(result).toBeDefined();
      // Note: Compression is placeholder implementation
    });

    test('should optimize content with lazy loading', async () => {
      const content = '<img src="test.jpg" alt="Test"><img src="test2.jpg" alt="Test2">';
      const optimizations = [{
        type: 'lazy-load' as const,
        enabled: true
      }];

      const result = await formatter.optimizeContent(content, optimizations);

      expect(result).toContain('loading="lazy"');
      expect(result.split('loading="lazy"')).toHaveLength(3); // Two images + split result
    });

    test('should skip disabled optimizations', async () => {
      const content = '<html><body><h1>Test</h1></body></html>';
      const optimizations = [{
        type: 'minify' as const,
        enabled: false
      }];

      const result = await formatter.optimizeContent(content, optimizations);

      expect(result).toBe(content); // Should remain unchanged
    });
  });

  describe('Format-Specific Processing', () => {
    test('should convert markdown to HTML correctly', () => {
      const markdown = '# Heading\n## Subheading\n**Bold** and *italic* text\n`code`';
      
      // Direct access to private method for surgical testing
      const convertMarkdownToHTML = (formatter as any)._convertMarkdownToHTML.bind(formatter);
      const result = convertMarkdownToHTML(markdown);

      expect(result).toContain('<h1>Heading</h1>');
      expect(result).toContain('<h2>Subheading</h2>');
      expect(result).toContain('<strong>Bold</strong>');
      expect(result).toContain('<em>italic</em>');
      expect(result).toContain('<code>code</code>');
    });

    test('should generate CSS for HTML output', () => {
      const options = {
        format: 'html' as const,
        responsiveDesign: true,
        accessibility: true,
        includeNavigation: false,
        includeSearch: false
      };

      // Direct access to private method for surgical testing
      const generateCSS = (formatter as any)._generateCSS.bind(formatter);
      const css = generateCSS(options);

      expect(css).toContain('font-family: Arial, sans-serif');
      expect(css).toContain('@media (max-width: 768px)');
      expect(css).toContain(':focus');
      expect(css).toContain('.sr-only');
    });

    test('should generate JavaScript for HTML output', () => {
      const options = {
        format: 'html' as const,
        includeNavigation: true,
        includeSearch: true,
        responsiveDesign: false,
        accessibility: false
      };

      // Direct access to private method for surgical testing
      const generateJavaScript = (formatter as any)._generateJavaScript.bind(formatter);
      const js = generateJavaScript(options);

      expect(js).toContain('function searchDocumentation');
      expect(js).toContain('function initializeNavigation');
      expect(js).toContain('document.addEventListener');
    });

    test('should minify different content types correctly', async () => {
      // Direct access to private method for surgical testing
      const minifyContent = (formatter as any)._minifyContent.bind(formatter);

      const htmlContent = '<html>  <body>  <h1>Test</h1>  </body>  </html>';
      const cssContent = 'body { margin: 0; padding: 0; }';
      const jsContent = 'function test() { return true; }';

      const minifiedHTML = await minifyContent(htmlContent, 'html');
      const minifiedCSS = await minifyContent(cssContent, 'css');
      const minifiedJS = await minifyContent(jsContent, 'js');

      expect(minifiedHTML).not.toContain('  ');
      expect(minifiedCSS).not.toContain(' ');
      expect(minifiedJS).not.toContain(' ');
    });
  });

  describe('Performance and Memory Safety', () => {
    test('should meet performance requirements (<10ms)', async () => {
      const startTime = Date.now();
      
      const mockContent = {
        sections: [{
          id: 'test',
          title: 'Test',
          content: 'Test content',
          subsections: [],
          order: 1,
          metadata: {}
        }],
        tableOfContents: [],
        metadata: {
          generatedAt: '2025-09-16T16:00:00Z',
          generatedBy: 'DocumentationGeneratorCore',
          version: '1.0.0',
          apiVersion: '1.0.0',
          totalSections: 1,
          estimatedReadingTime: 1,
          generationTime: 5,
          templateUsed: 'markdown'
        },
        appendices: []
      };

      const options = {
        format: 'markdown' as const,
        includeNavigation: false,
        includeSearch: false,
        responsiveDesign: false,
        accessibility: false
      };

      await formatter.formatDocumentation(mockContent, options);

      const executionTime = Date.now() - startTime;
      expect(executionTime).toBeLessThan(10); // <10ms requirement
    });

    test('should implement MEM-SAFE-002 compliance', () => {
      // Verify inheritance from BaseTrackingService
      expect(formatter).toBeInstanceOf(DocumentationFormatter);
      
      // Verify resilient timing dual-field pattern
      expect((formatter as any)._resilientTimer).toBeDefined();
      expect((formatter as any)._metricsCollector).toBeDefined();
    });

    test('should handle memory cleanup properly', async () => {
      await formatter.initialize();
      
      // Verify caches are initialized
      expect((formatter as any)._formattingCache).toBeDefined();
      expect((formatter as any)._assetCache).toBeDefined();

      await formatter.shutdown();
      
      // Verify caches are cleared
      expect((formatter as any)._formattingCache.size).toBe(0);
      expect((formatter as any)._assetCache.size).toBe(0);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle null/undefined inputs gracefully', async () => {
      const options = {
        format: 'markdown' as const,
        includeNavigation: false,
        includeSearch: false,
        responsiveDesign: false,
        accessibility: false
      };

      await expect(formatter.formatDocumentation(null as any, options)).rejects.toThrow();
      await expect(formatter.formatDocumentation(undefined as any, options)).rejects.toThrow();
    });

    test('should handle unsupported format gracefully', async () => {
      const mockContent = {
        sections: [],
        tableOfContents: [],
        metadata: {
          generatedAt: '2025-09-16T16:00:00Z',
          generatedBy: 'DocumentationGeneratorCore',
          version: '1.0.0',
          apiVersion: '1.0.0',
          totalSections: 0,
          estimatedReadingTime: 0,
          generationTime: 0,
          templateUsed: 'markdown'
        },
        appendices: []
      };

      const options = {
        format: 'unsupported' as any,
        includeNavigation: false,
        includeSearch: false,
        responsiveDesign: false,
        accessibility: false
      };

      await expect(formatter.formatDocumentation(mockContent, options)).rejects.toThrow('Unsupported format');
    });

    test('should handle formatting errors gracefully', async () => {
      // Mock a method to throw an error
      const originalFormatContentByType = (formatter as any)._formatContentByType;
      (formatter as any)._formatContentByType = jest.fn(() => Promise.reject(new Error('Formatting error')));

      const mockContent = {
        sections: [],
        tableOfContents: [],
        metadata: {
          generatedAt: '2025-09-16T16:00:00Z',
          generatedBy: 'DocumentationGeneratorCore',
          version: '1.0.0',
          apiVersion: '1.0.0',
          totalSections: 0,
          estimatedReadingTime: 0,
          generationTime: 0,
          templateUsed: 'markdown'
        },
        appendices: []
      };

      const options = {
        format: 'markdown' as const,
        includeNavigation: false,
        includeSearch: false,
        responsiveDesign: false,
        accessibility: false
      };

      await expect(formatter.formatDocumentation(mockContent, options)).rejects.toThrow('Formatting error');

      // Restore original method
      (formatter as any)._formatContentByType = originalFormatContentByType;
    });
  });

  describe('Cache Management', () => {
    test('should manage cache size limits', async () => {
      const mockContent = {
        sections: [],
        tableOfContents: [],
        metadata: {
          generatedAt: '2025-09-16T16:00:00Z',
          generatedBy: 'DocumentationGeneratorCore',
          version: '1.0.0',
          apiVersion: '1.0.0',
          totalSections: 0,
          estimatedReadingTime: 0,
          generationTime: 0,
          templateUsed: 'markdown'
        },
        appendices: []
      };

      // Force cache to exceed limits by creating many unique formatting operations
      const promises: Promise<TFormattedOutput>[] = [];
      for (let i = 0; i < 40; i++) { // Exceed CACHE_SIZE_LIMIT of 30
        const options = {
          format: 'markdown' as const,
          includeNavigation: false,
          includeSearch: false,
          responsiveDesign: false,
          accessibility: false,
          theme: `theme-${i}` // Make each operation unique
        };
        promises.push(formatter.formatDocumentation(mockContent, options));
      }

      await Promise.all(promises);

      // Trigger cache cleanup
      (formatter as any)._cleanupCaches();

      const cacheSize = (formatter as any)._formattingCache.size;
      expect(cacheSize).toBeLessThanOrEqual(30); // Should respect cache limit
    });

    test('should update performance metrics correctly', () => {
      (formatter as any)._updatePerformanceMetrics();

      // Verify metrics are being recorded
      const metricsCollector = (formatter as any)._metricsCollector;
      expect(metricsCollector.recordValue).toHaveBeenCalled();
    });
  });

  describe('Integration Testing', () => {
    test('should integrate with Enhanced Orchestration Driver v6.4.0', () => {
      // Verify Enhanced Orchestration Driver integration
      expect(formatter).toBeInstanceOf(DocumentationFormatter);
      
      // Verify resilient timing integration
      expect((formatter as any)._resilientTimer).toBeDefined();
      expect((formatter as any)._metricsCollector).toBeDefined();
    });

    test('should maintain consistent formatting results', async () => {
      const mockContent = {
        sections: [{
          id: 'consistent-test',
          title: 'Consistent Test',
          content: 'Consistent content',
          subsections: [],
          order: 1,
          metadata: {}
        }],
        tableOfContents: [],
        metadata: {
          generatedAt: '2025-09-16T16:00:00Z',
          generatedBy: 'DocumentationGeneratorCore',
          version: '1.0.0',
          apiVersion: '1.0.0',
          totalSections: 1,
          estimatedReadingTime: 1,
          generationTime: 5,
          templateUsed: 'markdown'
        },
        appendices: []
      };

      const options = {
        format: 'markdown' as const,
        includeNavigation: true,
        includeSearch: false,
        responsiveDesign: false,
        accessibility: false
      };

      // Run multiple formatting operations sequentially to test caching
      const result1 = await formatter.formatDocumentation(mockContent, options);
      const result2 = await formatter.formatDocumentation(mockContent, options);
      const result3 = await formatter.formatDocumentation(mockContent, options);

      // All results should be identical (cached)
      expect(result1.id).toBe(result2.id);
      expect(result2.id).toBe(result3.id);
    });
  });
});
