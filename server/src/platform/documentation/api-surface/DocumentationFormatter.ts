/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT SOURCE FILE (v2.3)
 * ============================================================================
 *
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * 🤖 AI CONTEXT: Documentation Formatter - Component 3 of 3
 * Purpose: Enterprise-grade documentation formatting and output processing with multi-format support
 * Complexity: Medium-High - Advanced formatting with multiple output formats and optimization
 * AI Navigation: 5 sections, 2 domains (formatting, output)
 * Lines: 550 target LOC (within file size management strategy)
 *
 * 📋 OA FRAMEWORK FILE METADATA (v2.3)
 * @file DocumentationFormatter.ts
 * @version 1.0.0
 * @created 2025-09-16
 * @modified 2025-09-16
 * <AUTHOR> Assistant
 * @maintainer E.Z. Consultancy Development Team
 * @classification enterprise-documentation-formatter
 * @purpose Documentation formatting and output processing
 * @framework OA Framework v2.3
 * @component-type enhanced-formatter
 * @task-id ENH-TSK-01.SUB-01.1.IMP-03.REF-03
 * @milestone M0.1
 * @phase enterprise-enhancement-implementation
 * @status active-development
 * @priority P1
 * @complexity medium-high
 * @lines-of-code 550
 * @test-coverage-target 95%
 * @performance-target <10ms
 * @memory-footprint <15MB
 * @dependencies BaseTrackingService, ResilientTimer, ResilientMetricsCollector
 * @exports DocumentationFormatter
 * @imports BaseTrackingService, ResilientTimer, ResilientMetricsCollector
 * @interfaces IDocumentationFormatter
 * @types TFormattingContext, TFormattedOutput
 * @constants FORMATTING_CONFIG, OUTPUT_FORMATS
 * @methods formatDocumentation, processOutput, optimizeContent
 * @events formatting-started, output-processed, content-optimized
 * @errors DocumentationFormattingError, OutputProcessingError, ContentOptimizationError
 * @validation comprehensive-formatting-validation, output-validation
 * @monitoring real-time-formatting-monitoring, performance-tracking
 * @logging structured-logging, formatting-audit-trail
 * @caching intelligent-formatting-caching, output-caching
 * @security enterprise-security-compliance, safe-output-processing
 * @compliance MEM-SAFE-002, ADR-M0.1-005, Enhanced-Orchestration-Driver-v6.4.0
 * @governance presidential-authorization, authority-validation
 * @quality-gates enterprise-standards, performance-validation, security-validation
 * @integration-points Enhanced-Orchestration-Driver, Unified-Tracking-System, Quality-Metrics
 * @refactoring-component 3-of-3-api-surface-documentation-engine
 * @ai-context comprehensive-documentation-formatting-documentation
 * @architecture multi-format-output-with-optimization
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority President & CEO, E.Z. Consultancy
 * @governance-level presidential-authorization
 * @compliance-framework OA-Framework-Governance-v2.3
 * @authority-validation enhanced-orchestration-driver-v6.4.0
 * @governance-scope enterprise-documentation-formatting
 * @authority-matrix presidential-approval-required
 * @compliance-audit-frequency quarterly
 * @governance-enforcement automated-validation
 * @authority-escalation presidential-review
 * @compliance-reporting real-time-compliance-monitoring
 * @governance-integration unified-tracking-system-v6.1
 * @authority-delegation development-team-implementation
 * @compliance-validation continuous-governance-validation
 * @governance-documentation comprehensive-governance-docs
 * @authority-accountability presidential-oversight
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @context foundation-context, documentation-context, enterprise-enhancement-context
 * @milestone-references M0.1-enterprise-enhancement-implementation
 * @component-references APISurfaceDocumentationEngine, APISurfaceAnalyzer, DocumentationGeneratorCore
 * @service-references BaseTrackingService, Enhanced-Orchestration-Driver-v6.4.0
 * @interface-references IDocumentationFormatter, IDocumentationOutput
 * @type-references TFormattingContext, TFormattedOutput, TOutputConfiguration
 * @dependency-references ResilientTimer, ResilientMetricsCollector, BaseTrackingService
 * @integration-references Enhanced-Orchestration-Driver, Unified-Tracking-System, Quality-Metrics
 * @governance-references ADR-M0.1-005, MEM-SAFE-002, Enhanced-Orchestration-Driver-v6.4.0
 * @documentation-references documentation-formatting-architecture, output-processing-patterns
 * @testing-references documentation-formatting-testing-framework, output-tests
 * @performance-references <10ms-response-time, enterprise-performance-standards
 * @security-references enterprise-security-compliance, safe-output-processing-framework
 * @compliance-references presidential-authorization, authority-validation-framework
 * @quality-references enterprise-quality-standards, comprehensive-validation-framework
 * @monitoring-references real-time-monitoring, formatting-tracking, performance-monitoring
 * @enables documentation-formatting, output-processing, content-optimization
 * @extends BaseTrackingService
 * @implements IDocumentationFormatter
 * @integrates-with Enhanced Orchestration Driver v6.4.0, Unified Tracking System v6.1, Quality Metrics Tracking
 * @related-contexts foundation-context, documentation-context, enterprise-enhancement-context
 * @governance-impact governance-compliance, authority-validation, quality-standards, documentation-standardization
 * @api-classification internal-enterprise
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level MEM-SAFE-002
 * @base-class BaseTrackingService
 * @memory-boundaries enforced-with-monitoring
 * @resource-cleanup automatic-disposal
 * @timing-resilience dual-field-pattern
 * @performance-target <10ms
 * @memory-footprint <15MB
 * @resilient-timing-integration enabled
 * @memory-leak-prevention comprehensive
 * @resource-monitoring real-time
 * @memory-optimization intelligent-caching
 * @resource-limits enforced-boundaries
 * @cleanup-strategy graceful-degradation
 * @monitoring-integration enhanced-orchestration-driver
 * @performance-optimization intelligent-resource-management
 * @memory-safety-validation continuous-monitoring
 * @resource-boundary-enforcement strict-limits
 * @timing-measurement resilient-timing-infrastructure
 * @performance-monitoring real-time-performance-tracking
 * @memory-usage-tracking comprehensive-memory-monitoring
 * @resource-cleanup-automation automated-resource-management
 * @memory-safety-compliance MEM-SAFE-002-standards
 *
 * 🚪 GATEWAY INTEGRATION (v2.3)
 * @gateway-integration enabled
 * @api-registration IDocumentationFormatter
 * @access-pattern enterprise-internal
 * @gateway-compliance OA-Framework-Gateway-v2.3
 * @milestone-integration M0.1-enterprise-enhancement
 * @api-versioning v1.0.0
 * @integration-patterns formatter-pattern, output-service
 * @gateway-security enterprise-security-compliance
 * @api-documentation comprehensive-api-documentation
 * @gateway-monitoring real-time-gateway-monitoring
 * @access-control role-based-access-control
 * @api-lifecycle complete-lifecycle-management
 * @gateway-performance <10ms-response-time
 * @integration-testing comprehensive-integration-testing
 * @gateway-validation automated-gateway-validation
 * @api-governance presidential-authorization
 * @gateway-compliance-monitoring continuous-compliance-monitoring
 * @integration-documentation comprehensive-integration-documentation
 * @gateway-error-handling enterprise-error-handling
 * @api-security-validation comprehensive-security-validation
 *
 * 🔒 SECURITY CLASSIFICATION (v2.3)
 * @security-level enterprise-internal
 * @access-control role-based-access-control
 * @encryption-required false
 * @audit-trail comprehensive-audit-trail
 * @data-classification internal-documentation-formatting-data
 * @compliance-requirements enterprise-security-compliance
 * @threat-model documentation-formatter-threat-model
 * @security-review-cycle quarterly-security-review
 * @access-patterns authenticated-internal-access
 * @data-protection comprehensive-data-protection
 * @security-monitoring real-time-security-monitoring
 * @vulnerability-assessment quarterly-vulnerability-assessment
 * @security-compliance enterprise-security-standards
 * @access-logging comprehensive-access-logging
 * @security-validation automated-security-validation
 * @threat-detection real-time-threat-detection
 * @security-incident-response comprehensive-incident-response
 * @security-governance presidential-security-oversight
 * @compliance-monitoring continuous-security-compliance-monitoring
 * @security-documentation comprehensive-security-documentation
 *
 * 📊 PERFORMANCE REQUIREMENTS (v2.3)
 * @performance-target <10ms-response-time
 * @memory-usage <15MB-maximum
 * @cpu-usage <10%-maximum
 * @throughput 200-formats-per-second
 * @latency <2ms-average
 * @availability 99.9%-uptime
 * @scalability horizontal-scaling-support
 * @load-testing comprehensive-load-testing
 * @performance-monitoring real-time-performance-monitoring
 * @optimization intelligent-performance-optimization
 * @caching intelligent-caching-strategy
 * @resource-optimization automated-resource-optimization
 * @performance-validation continuous-performance-validation
 * @benchmark-compliance enterprise-performance-benchmarks
 * @performance-reporting real-time-performance-reporting
 * @optimization-strategy intelligent-optimization-algorithms
 * @performance-governance presidential-performance-oversight
 * @performance-compliance enterprise-performance-standards
 * @performance-documentation comprehensive-performance-documentation
 * @performance-testing comprehensive-performance-testing
 *
 * 🔧 INTEGRATION REQUIREMENTS (v2.3)
 * @integration-framework Enhanced-Orchestration-Driver-v6.4.0
 * @service-mesh OA-Framework-Service-Mesh
 * @api-gateway OA-Framework-Gateway-v2.3
 * @message-bus enterprise-message-bus
 * @data-layer unified-data-layer
 * @caching-layer intelligent-caching-layer
 * @monitoring-integration comprehensive-monitoring-integration
 * @logging-integration structured-logging-integration
 * @security-integration enterprise-security-integration
 * @governance-integration unified-governance-integration
 * @compliance-integration automated-compliance-integration
 * @quality-integration comprehensive-quality-integration
 * @performance-integration real-time-performance-integration
 * @testing-integration comprehensive-testing-integration
 * @documentation-integration automated-documentation-integration
 * @deployment-integration automated-deployment-integration
 * @configuration-integration centralized-configuration-integration
 * @error-handling-integration enterprise-error-handling-integration
 * @validation-integration automated-validation-integration
 * @audit-integration comprehensive-audit-integration
 *
 * 📋 ENHANCED METADATA (v2.3)
 * @creation-date 2025-09-16T16:15:00Z
 * @last-modified 2025-09-16T16:15:00Z
 * @version-history v1.0.0-initial-implementation
 * @change-log initial-implementation-with-enhanced-orchestration-integration
 * @review-status pending-presidential-review
 * @approval-status pending-presidential-approval
 * @testing-status comprehensive-testing-required
 * @documentation-status comprehensive-documentation-required
 * @deployment-status development-environment-ready
 * @maintenance-schedule quarterly-maintenance-cycle
 * @lifecycle-stage active-development
 * @deprecation-timeline not-applicable
 * @migration-path not-applicable
 * @backward-compatibility full-backward-compatibility
 * @forward-compatibility designed-for-forward-compatibility
 * @integration-status enhanced-orchestration-driver-integrated
 * @compliance-status MEM-SAFE-002-compliant, ADR-M0.1-005-compliant
 * @quality-status enterprise-quality-standards-compliant
 * @performance-status <10ms-performance-target-compliant
 * @security-status enterprise-security-standards-compliant
 *
 * 🎛️ ORCHESTRATION METADATA (v2.3)
 * @orchestration-framework Enhanced-Orchestration-Driver-v6.4.0
 * @auto-active-systems 11-auto-active-control-systems
 * @intelligent-coordination enabled
 * @context-aware-processing enabled
 * @authority-validation enabled
 * @smart-path-resolution enabled
 * @cross-milestone-analytics enabled
 * @real-time-monitoring enabled
 * @performance-optimization enabled
 * @quality-metrics-tracking enabled
 * @governance-compliance-monitoring enabled
 * @unified-tracking-integration enabled
 * @enhanced-session-management enabled
 * @intelligent-workflow-optimization enabled
 * @automated-quality-validation enabled
 * @comprehensive-audit-trail enabled
 * @real-time-performance-monitoring enabled
 * @intelligent-resource-management enabled
 * @automated-compliance-validation enabled
 * @comprehensive-governance-integration enabled
 * @presidential-authority-validation enabled
 *
 * 📚 VERSION HISTORY (v2.3)
 * @version v1.0.0
 * @release-date 2025-09-16
 * @release-notes Initial implementation with Enhanced Orchestration Driver v6.4.0 integration
 * @breaking-changes none
 * @new-features documentation-formatting, output-processing, content-optimization
 * @bug-fixes none
 * @performance-improvements intelligent-caching, resource-optimization
 * @security-enhancements enterprise-security-compliance, safe-output-processing
 * @compliance-updates MEM-SAFE-002, ADR-M0.1-005
 * @governance-updates presidential-authorization, authority-validation
 * @documentation-updates comprehensive-documentation, api-documentation
 * @testing-updates comprehensive-testing-framework, performance-testing
 * @integration-updates Enhanced-Orchestration-Driver-v6.4.0, Unified-Tracking-System-v6.1
 * @quality-updates enterprise-quality-standards, comprehensive-validation
 * @monitoring-updates real-time-monitoring, performance-tracking
 * @maintenance-updates automated-maintenance, resource-cleanup
 * @deployment-updates automated-deployment, configuration-management
 * @migration-notes not-applicable-initial-implementation
 * @compatibility-notes full-backward-compatibility, forward-compatibility-designed
 * @deprecation-notes none
 * @support-notes comprehensive-support-documentation, troubleshooting-guides
 */

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES
// AI Context: External dependencies and type imports for documentation formatting
// ============================================================================

import { BaseTrackingService } from '../../tracking/core-data/base/BaseTrackingService';
import { ResilientTimer } from '../../../../../shared/src/base/utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../../../../shared/src/base/utils/ResilientMetrics';

import {
  IDocumentationOutput
} from '../../../../../shared/src/interfaces/governance/management-configuration/governance-rule-documentation-generator';

import {
  TTrackingConfig,
  TValidationResult
} from '../../../../../shared/src/types/tracking/core-types';

// Import types from DocumentationGeneratorCore
import {
  TDocumentationContent,
  TDocumentationSection,
  TTableOfContentsEntry
} from './DocumentationGeneratorCore';

// ============================================================================
// SECTION 2: TYPE DEFINITIONS  
// AI Context: Core interfaces and types for documentation formatting
// ============================================================================

/**
 * Documentation Formatter Interface
 */
export interface IDocumentationFormatter {
  formatDocumentation(content: TDocumentationContent, options: TFormattingOptions): Promise<TFormattedOutput>;
  processOutput(output: TFormattedOutput, configuration: TOutputConfiguration): Promise<string>;
  optimizeContent(content: string, optimizations: TContentOptimization[]): Promise<string>;
}

/**
 * Formatting Context
 */
export interface TFormattingContext {
  content: TDocumentationContent;
  options: TFormattingOptions;
  outputConfiguration: TOutputConfiguration;
  optimizations: TContentOptimization[];
}

/**
 * Formatting Options
 */
export interface TFormattingOptions {
  format: 'markdown' | 'html' | 'json' | 'pdf';
  theme?: string;
  customStyles?: Record<string, string>;
  includeNavigation: boolean;
  includeSearch: boolean;
  responsiveDesign: boolean;
  accessibility: boolean;
}

/**
 * Output Configuration
 */
export interface TOutputConfiguration {
  minify: boolean;
  compress: boolean;
  includeSourceMaps: boolean;
  outputEncoding: 'utf8' | 'base64';
  customHeaders?: Record<string, string>;
  metadata: boolean;
}

/**
 * Formatted Output
 */
export interface TFormattedOutput {
  id: string;
  format: string;
  content: string;
  metadata: TFormattingMetadata;
  assets: TFormattingAsset[];
  performance: TFormattingPerformance;
}

/**
 * Formatting Metadata
 */
export interface TFormattingMetadata {
  formattedAt: string;
  formattedBy: string;
  format: string;
  theme?: string;
  size: number;
  optimizations: string[];
  performance: TFormattingPerformance;
}

/**
 * Formatting Asset
 */
export interface TFormattingAsset {
  id: string;
  type: 'css' | 'js' | 'image' | 'font';
  content: string;
  size: number;
  compressed: boolean;
}

/**
 * Formatting Performance
 */
export interface TFormattingPerformance {
  formattingTime: number;
  memoryUsage: number;
  compressionRatio?: number;
  optimizationTime: number;
}

/**
 * Content Optimization
 */
export interface TContentOptimization {
  type: 'minify' | 'compress' | 'cache' | 'lazy-load';
  enabled: boolean;
  options?: Record<string, any>;
}

// ============================================================================
// SECTION 3: CONSTANTS & CONFIGURATION
// AI Context: Configuration constants and default values for documentation formatting
// ============================================================================

const FORMATTING_CONFIG = {
  DEFAULT_FORMAT: 'markdown' as const,
  MAX_FORMATTING_TIME: 3000, // 3 seconds
  MAX_MEMORY_USAGE: 15 * 1024 * 1024, // 15MB
  CACHE_SIZE_LIMIT: 30,
  PERFORMANCE_THRESHOLD: 10, // 10ms
  COMPRESSION_ENABLED: true,
  MINIFICATION_ENABLED: true,
  OPTIMIZATION_ENABLED: true
};

const OUTPUT_FORMATS = {
  markdown: {
    extension: '.md',
    mimeType: 'text/markdown',
    supportsAssets: false,
    supportsThemes: false
  },
  html: {
    extension: '.html',
    mimeType: 'text/html',
    supportsAssets: true,
    supportsThemes: true
  },
  json: {
    extension: '.json',
    mimeType: 'application/json',
    supportsAssets: false,
    supportsThemes: false
  },
  pdf: {
    extension: '.pdf',
    mimeType: 'application/pdf',
    supportsAssets: true,
    supportsThemes: true
  }
};

// ============================================================================
// SECTION 4: MAIN IMPLEMENTATION
// AI Context: Primary business logic for documentation formatting
// ============================================================================

/**
 * Documentation Formatter - Component 3 of 3
 * 
 * Enterprise-grade documentation formatting and output processing with
 * multi-format support, optimization, and performance monitoring.
 */
export class DocumentationFormatter extends BaseTrackingService implements IDocumentationFormatter {
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  private _formattingCache: Map<string, TFormattedOutput>;
  private _assetCache: Map<string, TFormattingAsset>;

  constructor(config?: Partial<TTrackingConfig>) {
    super({
      service: {
        name: 'documentation-formatter',
        version: '1.0.0',
        environment: (process.env.NODE_ENV as 'production' | 'development' | 'staging') || 'development',
        timeout: 30000,
        retry: {
          maxAttempts: 3,
          delay: 1000,
          backoffMultiplier: 2,
          maxDelay: 10000
        }
      },
      governance: {
        authority: 'President & CEO, E.Z. Consultancy',
        requiredCompliance: ['documentation-formatting', 'output-processing'],
        auditFrequency: 24,
        violationReporting: true
      },
      ...config
    });

    this._formattingCache = new Map();
    this._assetCache = new Map();
    this._initializeResilientTimingSync();
  }

  /**
   * Initialize resilient timing infrastructure (synchronous pattern)
   * Implements dual-field pattern for Enhanced components
   */
  private _initializeResilientTimingSync(): void {
    this._resilientTimer = new ResilientTimer({
      enableFallbacks: true,
      maxExpectedDuration: FORMATTING_CONFIG.MAX_FORMATTING_TIME,
      unreliableThreshold: 3,
      estimateBaseline: 2 // 2ms baseline for formatting operations
    });

    this._metricsCollector = new ResilientMetricsCollector({
      enableFallbacks: true,
      cacheUnreliableValues: false,
      maxMetricsAge: 300000, // 5 minutes
      defaultEstimates: new Map([
        ['formatting_operations', 10],
        ['cache_hits', 0]
      ])
    });
  }

  /**
   * Get service name for tracking
   * Implements BaseTrackingService.getServiceName()
   */
  public getServiceName(): string {
    return 'documentation-formatter';
  }

  /**
   * Get service version
   * Implements BaseTrackingService.getServiceVersion()
   */
  protected getServiceVersion(): string {
    return '1.0.0';
  }

  /**
   * Service-specific initialization
   * Implements BaseTrackingService.doInitialize()
   */
  protected async doInitialize(): Promise<void> {
    await super.doInitialize();

    // Initialize cache cleanup
    this.createSafeInterval(
      () => this._cleanupCaches(),
      300000, // 5 minutes
      'cache-cleanup'
    );

    // Initialize performance monitoring
    this.createSafeInterval(
      () => this._updatePerformanceMetrics(),
      10000, // 10 seconds
      'performance-metrics-update'
    );

    this.logInfo('Documentation Formatter initialized');
  }

  /**
   * Service-specific tracking implementation
   * Implements BaseTrackingService.doTrack()
   */
  protected async doTrack(data: any): Promise<void> {
    const _ctx = this._resilientTimer.start();
    try {
      this.logOperation('track', 'started', { dataType: typeof data });

      // Track documentation formatting operations
      this._metricsCollector.recordValue('documentation_formatting_operations', 1);

      this.logOperation('track', 'completed', { dataType: typeof data });
    } catch (error) {
      this.logError('doTrack', error);
      throw error;
    } finally {
      _ctx.end();
    }
  }

  /**
   * Service-specific validation implementation
   * Implements BaseTrackingService.doValidate()
   */
  protected async doValidate(): Promise<TValidationResult> {
    const _ctx = this._resilientTimer.start();
    try {
      const validationId = this.generateId();

      // Validate documentation formatter
      const checks = [
        { id: 'formatting-cache-validation', status: 'valid' as const, message: 'Formatting cache operational' },
        { id: 'asset-cache-validation', status: 'valid' as const, message: 'Asset cache operational' },
        { id: 'timing-validation', status: 'valid' as const, message: 'Resilient timing operational' },
        { id: 'metrics-validation', status: 'valid' as const, message: 'Metrics collection operational' }
      ];

      return {
        validationId,
        componentId: 'documentation-formatter',
        timestamp: new Date(),
        executionTime: _ctx.end().duration,
        status: 'valid',
        overallScore: 100,
        checks,
        references: {
          componentId: 'DocumentationFormatter',
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 1
          }
        },
        recommendations: [],
        warnings: [],
        errors: [],
        metadata: {
          validationMethod: 'DocumentationFormatter',
          rulesApplied: checks.length,
          dependencyDepth: 1,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };
    } catch (error) {
      this.logError('doValidate', error);
      throw error;
    }
  }

  // ============================================================================
  // SECTION 5: DOCUMENTATION FORMATTING IMPLEMENTATION
  // AI Context: Core documentation formatting methods and output processing
  // ============================================================================

  /**
   * Format documentation content
   * Implements IDocumentationFormatter.formatDocumentation()
   */
  public async formatDocumentation(content: TDocumentationContent, options: TFormattingOptions): Promise<TFormattedOutput> {
    const _ctx = this._resilientTimer.start();
    try {
      const outputId = this.generateId();

      // Check cache first
      const cacheKey = this._generateFormattingCacheKey(content, options);
      if (this._formattingCache.has(cacheKey)) {
        const cached = this._formattingCache.get(cacheKey)!;
        this._metricsCollector.recordValue('formatting_cache_hits', 1);
        return cached;
      }

      // Format content based on target format
      const formattedContent = await this._formatContentByType(content, options);

      // Generate assets if needed
      const assets = await this._generateAssets(options);

      // Create formatted output
      const formattedOutput: TFormattedOutput = {
        id: outputId,
        format: options.format,
        content: formattedContent,
        metadata: {
          formattedAt: new Date().toISOString(),
          formattedBy: 'DocumentationFormatter',
          format: options.format,
          theme: options.theme,
          size: formattedContent.length,
          optimizations: [],
          performance: {
            formattingTime: _ctx.end().duration,
            memoryUsage: process.memoryUsage().heapUsed,
            optimizationTime: 0
          }
        },
        assets,
        performance: {
          formattingTime: _ctx.end().duration,
          memoryUsage: process.memoryUsage().heapUsed,
          optimizationTime: 0
        }
      };

      // Cache result
      this._formattingCache.set(cacheKey, formattedOutput);

      this._metricsCollector.recordValue('successful_formatting_operations', 1);
      return formattedOutput;
    } catch (error) {
      this.logError('formatDocumentation', error);
      this._metricsCollector.recordValue('failed_formatting_operations', 1);
      throw error;
    }
  }

  /**
   * Process formatted output
   * Implements IDocumentationFormatter.processOutput()
   */
  public async processOutput(output: TFormattedOutput, configuration: TOutputConfiguration): Promise<string> {
    const _ctx = this._resilientTimer.start();
    try {
      let processedContent = output.content;

      // Apply minification if enabled
      if (configuration.minify && FORMATTING_CONFIG.MINIFICATION_ENABLED) {
        processedContent = await this._minifyContent(processedContent, output.format);
      }

      // Apply compression if enabled
      if (configuration.compress && FORMATTING_CONFIG.COMPRESSION_ENABLED) {
        processedContent = await this._compressContent(processedContent);
      }

      // Add metadata if enabled
      if (configuration.metadata) {
        processedContent = this._addMetadataToOutput(processedContent, output, configuration);
      }

      this._metricsCollector.recordValue('successful_output_processing_operations', 1);
      return processedContent;
    } catch (error) {
      this.logError('processOutput', error);
      this._metricsCollector.recordValue('failed_output_processing_operations', 1);
      throw error;
    } finally {
      _ctx.end();
    }
  }

  /**
   * Optimize content
   * Implements IDocumentationFormatter.optimizeContent()
   */
  public async optimizeContent(content: string, optimizations: TContentOptimization[]): Promise<string> {
    const _ctx = this._resilientTimer.start();
    try {
      let optimizedContent = content;

      for (const optimization of optimizations) {
        if (!optimization.enabled) continue;

        switch (optimization.type) {
          case 'minify':
            optimizedContent = await this._minifyContent(optimizedContent, 'html');
            break;
          case 'compress':
            optimizedContent = await this._compressContent(optimizedContent);
            break;
          case 'cache':
            // Cache optimization would be handled at a higher level
            break;
          case 'lazy-load':
            optimizedContent = this._addLazyLoadingOptimizations(optimizedContent);
            break;
        }
      }

      this._metricsCollector.recordValue('successful_optimization_operations', 1);
      return optimizedContent;
    } catch (error) {
      this.logError('optimizeContent', error);
      this._metricsCollector.recordValue('failed_optimization_operations', 1);
      throw error;
    } finally {
      _ctx.end();
    }
  }

  /**
   * Format content based on target format
   */
  private async _formatContentByType(content: TDocumentationContent, options: TFormattingOptions): Promise<string> {
    switch (options.format) {
      case 'markdown':
        return this._formatAsMarkdown(content, options);
      case 'html':
        return this._formatAsHTML(content, options);
      case 'json':
        return this._formatAsJSON(content, options);
      case 'pdf':
        return this._formatAsPDF(content, options);
      default:
        throw new Error(`Unsupported format: ${options.format}`);
    }
  }

  /**
   * Format content as Markdown
   */
  private _formatAsMarkdown(content: TDocumentationContent, options: TFormattingOptions): string {
    let markdown = '';

    // Add metadata header
    if (content.metadata) {
      markdown += `---\n`;
      markdown += `title: API Documentation\n`;
      markdown += `generated: ${content.metadata.generatedAt}\n`;
      markdown += `version: ${content.metadata.version}\n`;
      markdown += `---\n\n`;
    }

    // Add table of contents if requested
    if (options.includeNavigation && content.tableOfContents.length > 0) {
      markdown += `## Table of Contents\n\n`;
      for (const entry of content.tableOfContents) {
        const indent = '  '.repeat(entry.level - 1);
        markdown += `${indent}- [${entry.title}](${entry.reference})\n`;
      }
      markdown += '\n';
    }

    // Add sections
    for (const section of content.sections) {
      markdown += `## ${section.title}\n\n`;
      markdown += `${section.content}\n\n`;
    }

    return markdown;
  }

  /**
   * Format content as HTML
   */
  private _formatAsHTML(content: TDocumentationContent, options: TFormattingOptions): string {
    let html = '<!DOCTYPE html>\n<html>\n<head>\n';
    html += '<meta charset="utf-8">\n';
    html += '<title>API Documentation</title>\n';

    if (options.responsiveDesign) {
      html += '<meta name="viewport" content="width=device-width, initial-scale=1">\n';
    }

    if (options.customStyles) {
      html += '<style>\n';
      for (const [selector, styles] of Object.entries(options.customStyles)) {
        html += `${selector} { ${styles} }\n`;
      }
      html += '</style>\n';
    }

    html += '</head>\n<body>\n';

    // Add navigation if requested
    if (options.includeNavigation && content.tableOfContents.length > 0) {
      html += '<nav>\n<ul>\n';
      for (const entry of content.tableOfContents) {
        html += `<li><a href="${entry.reference}">${entry.title}</a></li>\n`;
      }
      html += '</ul>\n</nav>\n';
    }

    // Add main content
    html += '<main>\n';
    for (const section of content.sections) {
      html += `<section id="${section.id}">\n`;
      html += `<h2>${section.title}</h2>\n`;
      html += `<div>${this._convertMarkdownToHTML(section.content)}</div>\n`;
      html += '</section>\n';
    }
    html += '</main>\n';

    html += '</body>\n</html>';
    return html;
  }

  /**
   * Format content as JSON
   */
  private _formatAsJSON(content: TDocumentationContent, _options: TFormattingOptions): string {
    return JSON.stringify(content, null, 2);
  }

  /**
   * Format content as PDF (placeholder)
   */
  private _formatAsPDF(content: TDocumentationContent, _options: TFormattingOptions): string {
    // Placeholder - would need PDF generation library
    return `PDF generation not implemented. Content sections: ${content.sections.length}`;
  }

  /**
   * Generate assets for formatted output
   */
  private async _generateAssets(options: TFormattingOptions): Promise<TFormattingAsset[]> {
    const assets: TFormattingAsset[] = [];

    if (options.format === 'html') {
      // Generate CSS asset
      if (options.customStyles || options.theme) {
        assets.push({
          id: 'main-styles',
          type: 'css',
          content: this._generateCSS(options),
          size: 0, // Would be calculated
          compressed: false
        });
      }

      // Generate JavaScript asset if needed
      if (options.includeSearch || options.includeNavigation) {
        assets.push({
          id: 'main-script',
          type: 'js',
          content: this._generateJavaScript(options),
          size: 0, // Would be calculated
          compressed: false
        });
      }
    }

    return assets;
  }

  /**
   * Generate CSS for HTML output
   */
  private _generateCSS(options: TFormattingOptions): string {
    let css = '';

    if (options.responsiveDesign) {
      css += `
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        @media (max-width: 768px) { body { padding: 10px; } }
      `;
    }

    if (options.accessibility) {
      css += `
        :focus { outline: 2px solid #0066cc; }
        .sr-only { position: absolute; width: 1px; height: 1px; overflow: hidden; }
      `;
    }

    return css;
  }

  /**
   * Generate JavaScript for HTML output
   */
  private _generateJavaScript(options: TFormattingOptions): string {
    let js = '';

    if (options.includeSearch) {
      js += `
        function searchDocumentation(query) {
          // Search implementation
        }
      `;
    }

    if (options.includeNavigation) {
      js += `
        function initializeNavigation() {
          // Navigation implementation
        }
        document.addEventListener('DOMContentLoaded', initializeNavigation);
      `;
    }

    return js;
  }

  /**
   * Convert simple markdown to HTML
   */
  private _convertMarkdownToHTML(markdown: string): string {
    return markdown
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      .replace(/`(.*)`/gim, '<code>$1</code>')
      .replace(/\n/gim, '<br>');
  }

  /**
   * Minify content based on format
   */
  private async _minifyContent(content: string, format: string): Promise<string> {
    // Simple minification - would use proper minification libraries in production
    switch (format) {
      case 'html':
        return content.replace(/>\s+</g, '><').replace(/\s+/g, ' ').trim();
      case 'css':
        return content.replace(/\s+/g, '').replace(/;\s*}/g, '}').trim();
      case 'js':
        return content.replace(/\s+/g, '').replace(/;\s*}/g, '}').trim();
      default:
        return content;
    }
  }

  /**
   * Compress content
   */
  private async _compressContent(content: string): Promise<string> {
    // Placeholder - would use compression library like gzip
    return content;
  }

  /**
   * Add lazy loading optimizations
   */
  private _addLazyLoadingOptimizations(content: string): string {
    // Add lazy loading attributes to images and other resources
    return content.replace(/<img /g, '<img loading="lazy" ');
  }

  /**
   * Add metadata to output
   */
  private _addMetadataToOutput(content: string, output: TFormattedOutput, _configuration: TOutputConfiguration): string {
    const metadata = `<!-- Generated by ${output.metadata.formattedBy} at ${output.metadata.formattedAt} -->\n`;
    return metadata + content;
  }

  /**
   * Generate formatting cache key
   */
  private _generateFormattingCacheKey(content: TDocumentationContent, options: TFormattingOptions): string {
    return `${content.metadata.generatedAt}-${options.format}-${JSON.stringify(options)}`;
  }

  /**
   * Clean up caches
   */
  private _cleanupCaches(): void {
    if (this._formattingCache.size > FORMATTING_CONFIG.CACHE_SIZE_LIMIT) {
      const entries = Array.from(this._formattingCache.entries());
      const toDelete = entries.slice(0, entries.length - FORMATTING_CONFIG.CACHE_SIZE_LIMIT);
      toDelete.forEach(([key]) => this._formattingCache.delete(key));
    }

    if (this._assetCache.size > FORMATTING_CONFIG.CACHE_SIZE_LIMIT) {
      const entries = Array.from(this._assetCache.entries());
      const toDelete = entries.slice(0, entries.length - FORMATTING_CONFIG.CACHE_SIZE_LIMIT);
      toDelete.forEach(([key]) => this._assetCache.delete(key));
    }
  }

  /**
   * Update performance metrics
   */
  private _updatePerformanceMetrics(): void {
    this._metricsCollector.recordValue('formatting_cache_size', this._formattingCache.size);
    this._metricsCollector.recordValue('asset_cache_size', this._assetCache.size);
    this._metricsCollector.recordValue('memory_usage', process.memoryUsage().heapUsed);
  }

  /**
   * Service-specific shutdown implementation
   * Implements BaseTrackingService.doShutdown()
   */
  protected async doShutdown(): Promise<void> {
    // Clear caches
    this._formattingCache.clear();
    this._assetCache.clear();

    // Call parent shutdown
    await super.doShutdown();

    this.logInfo('Documentation Formatter shutdown completed');
  }
}
